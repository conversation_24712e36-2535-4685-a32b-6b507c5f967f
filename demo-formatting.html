<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Formatting Demo</title>
    <link rel="stylesheet" href="public/styles.css">
</head>
<body>
    <div class="container">
        <header>
            <h1>New Formatting Demo</h1>
            <p>This shows how the results will look with the improved formatting</p>
        </header>

        <main style="grid-template-columns: 1fr;">
            <div class="output-section">
                <div class="output-header">
                    <h3>Analysis Results Demo</h3>
                </div>
                <div class="output-content">
                    <div class="result-container">
                        <h3>Code Analysis Results</h3>
                        
                        <div class="analysis-section">
                            <div class="section-title">🔒 Security Analysis</div>
                            <div class="section-content">
                                <div class="analysis-item">✅ No security issues found</div>
                                <div class="analysis-item">✅ No use of eval() or dangerous functions</div>
                                <div class="analysis-item">✅ No XSS vulnerabilities detected</div>
                            </div>
                        </div>
                        
                        <div class="analysis-section">
                            <div class="section-title">⚡ Performance Analysis</div>
                            <div class="section-content">
                                <div class="analysis-item">✅ No performance issues found</div>
                                <div class="analysis-item">⚠️ Consider using const instead of let for immutable variables</div>
                            </div>
                        </div>
                        
                        <div class="analysis-section">
                            <div class="section-title">🚀 Optimization Suggestions</div>
                            <div class="section-content">
                                <div class="analysis-item">💡 Add input validation for parameters</div>
                                <div class="analysis-item">💡 Consider adding JSDoc comments</div>
                            </div>
                        </div>
                        
                        <div class="analysis-section">
                            <div class="section-title">✅ Functionality Assessment</div>
                            <div class="section-content">
                                <div class="analysis-item">📋 Function correctly implements addition of two numbers</div>
                                <div class="analysis-item">📋 Return type is appropriate for the operation</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="output-section" style="margin-top: 30px;">
                <div class="output-header">
                    <h3>Test Generation Demo</h3>
                </div>
                <div class="output-content">
                    <div class="result-container">
                        <h3>Test Generation Results</h3>
                        
                        <div class="test-section">
                            <div class="section-title">🧪 Generated Test Suite</div>
                            <div class="test-code">
                                <pre>describe('add function', () => {
  test('should add two positive numbers', () => {
    expect(add(2, 3)).toBe(5);
  });
  
  test('should add negative numbers', () => {
    expect(add(-1, -2)).toBe(-3);
  });
  
  test('should handle zero', () => {
    expect(add(0, 5)).toBe(5);
    expect(add(5, 0)).toBe(5);
  });
  
  test('should handle decimal numbers', () => {
    expect(add(1.5, 2.5)).toBe(4);
  });
});</pre>
                            </div>
                        </div>
                        
                        <div class="fixes-section">
                            <div class="section-title">🔧 Suggested Fixes</div>
                            <div class="fix-item">
                                <div class="fix-header">Fix #1</div>
                                <div class="issue-description">Issue: No input validation for non-numeric values</div>
                                <div class="fix-label">Fixed Code:</div>
                                <div class="fix-code">
                                    <pre>function add(a, b) {
  if (typeof a !== 'number' || typeof b !== 'number') {
    throw new Error('Both parameters must be numbers');
  }
  return a + b;
}</pre>
                                </div>
                            </div>
                            
                            <div class="fix-item">
                                <div class="fix-header">Fix #2</div>
                                <div class="issue-description">Issue: Missing JSDoc documentation</div>
                                <div class="fix-label">Fixed Code:</div>
                                <div class="fix-code">
                                    <pre>/**
 * Adds two numbers together
 * @param {number} a - First number
 * @param {number} b - Second number
 * @returns {number} The sum of a and b
 */
function add(a, b) {
  if (typeof a !== 'number' || typeof b !== 'number') {
    throw new Error('Both parameters must be numbers');
  }
  return a + b;
}</pre>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>
</body>
</html>
