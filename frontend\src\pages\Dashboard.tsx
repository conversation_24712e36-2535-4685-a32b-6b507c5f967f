import { useState, useEffect, useRef } from "react";
import { useAuth } from "@/contexts/AuthContext";
import { Navigate } from "react-router-dom";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import { Textarea } from "@/components/ui/textarea";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { 
  Code, 
  TestTube, 
  Shield, 
  Zap, 
  TrendingUp, 
  Clock, 
  CheckCircle, 
  AlertTriangle,
  BarChart3,
  Activity,
  Users,
  Target,
  Bug,
  Plus,
  Download,
  FileText,
  Brain,
  Sparkles,
  Database,
  Globe,
  Cpu,
  Eye,
  Star,
  ArrowUp,
  ArrowDown
} from "lucide-react";
import { toast } from "sonner";

const Dashboard = () => {
  const { isAuthenticated, isLoading, user } = useAuth();
  const [stats, setStats] = useState({
    linesAnalyzed: 0,
    languagesUsed: 0,
    apisSupported: 5,
    accuracyRate: 0
  });
  const [isPageLoaded, setIsPageLoaded] = useState(false);
  const [visibleElements, setVisibleElements] = useState(new Set());
  const [bugReports, setBugReports] = useState([]);
  const [testResults, setTestResults] = useState([]);
  const [recentActivity, setRecentActivity] = useState([]);

  // Animation refs
  const statsRef = useRef(null);
  const chartsRef = useRef(null);
  const reportsRef = useRef(null);

  useEffect(() => {
    // Loading animation
    const timer = setTimeout(() => {
      setIsPageLoaded(true);
    }, 500);

    // Intersection Observer for scroll animations
    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            const element = entry.target as HTMLElement;
            element.style.opacity = '1';
            element.style.transform = 'translateY(0) scale(1)';
            setVisibleElements(prev => new Set(prev).add(entry.target.className));
          }
        });
      },
      { threshold: 0.1, rootMargin: '50px' }
    );

    // Observe elements for scroll animations
    setTimeout(() => {
      const elementsToObserve = document.querySelectorAll(
        '.animate-on-scroll, .stat-card, .chart-card, .report-card'
      );
      elementsToObserve.forEach(el => observer.observe(el));
    }, 600);

    // Fetch user statistics
    fetchUserStats();
    fetchBugReports();
    fetchTestResults();
    fetchRecentActivity();

    return () => {
      clearTimeout(timer);
      observer.disconnect();
    };
  }, []);

  const fetchUserStats = async () => {
    try {
      const response = await fetch('/api/user/stats', {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });
      
      if (response.ok) {
        const data = await response.json();
        setStats(data.stats);
        animateCounters(data.stats);
      }
    } catch (error) {
      console.error('Failed to fetch stats:', error);
    }
  };

  const fetchBugReports = async () => {
    try {
      const response = await fetch('/api/bug-reports', {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });
      
      if (response.ok) {
        const data = await response.json();
        setBugReports(data.data.bugReports.slice(0, 5)); // Latest 5
      }
    } catch (error) {
      console.error('Failed to fetch bug reports:', error);
    }
  };

  const fetchTestResults = async () => {
    try {
      const response = await fetch('/api/test-history', {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });
      
      if (response.ok) {
        const data = await response.json();
        setTestResults(data.data.slice(0, 5)); // Latest 5
      }
    } catch (error) {
      console.error('Failed to fetch test results:', error);
    }
  };

  const fetchRecentActivity = async () => {
    // Mock recent activity for now
    setRecentActivity([
      { id: 1, type: 'analysis', description: 'Code analysis completed', time: '2 minutes ago', icon: Brain },
      { id: 2, type: 'test', description: 'Unit tests generated', time: '5 minutes ago', icon: TestTube },
      { id: 3, type: 'bug', description: 'Bug report created', time: '10 minutes ago', icon: Bug },
      { id: 4, type: 'security', description: 'Security scan completed', time: '15 minutes ago', icon: Shield },
      { id: 5, type: 'optimization', description: 'Performance optimized', time: '20 minutes ago', icon: Zap }
    ]);
  };

  const animateCounters = (targetStats) => {
    const duration = 2000;
    const steps = 60;
    const stepDuration = duration / steps;
    
    let currentStep = 0;
    const interval = setInterval(() => {
      currentStep++;
      const progress = currentStep / steps;
      
      setStats({
        linesAnalyzed: Math.floor(targetStats.linesAnalyzed * progress),
        languagesUsed: Math.floor(targetStats.languagesUsed * progress),
        apisSupported: Math.floor(targetStats.apisSupported * progress),
        accuracyRate: Math.floor(targetStats.accuracyRate * progress)
      });
      
      if (currentStep >= steps) {
        clearInterval(interval);
        setStats(targetStats);
      }
    }, stepDuration);
  };

  const exportToPDF = async (type, data) => {
    try {
      const response = await fetch('/api/export/pdf', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        },
        body: JSON.stringify({ type, data })
      });

      if (response.ok) {
        const blob = await response.blob();
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `${type}_report_${Date.now()}.pdf`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        window.URL.revokeObjectURL(url);
        toast.success('PDF exported successfully!');
      } else {
        toast.error('Failed to export PDF');
      }
    } catch (error) {
      toast.error('Export failed');
      console.error('Export error:', error);
    }
  };

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900">
        <div className="text-center">
          <div className="relative">
            <div className="w-20 h-20 border-4 border-purple-500/30 border-t-purple-500 rounded-full animate-spin mx-auto mb-4"></div>
            <div className="absolute inset-0 w-20 h-20 border-4 border-pink-500/30 border-b-pink-500 rounded-full animate-spin mx-auto" style={{animationDirection: 'reverse', animationDuration: '1.5s'}}></div>
          </div>
          <h2 className="text-2xl font-bold text-white mb-2 animate-pulse">Loading Dashboard</h2>
          <p className="text-purple-300 animate-pulse">Preparing your analytics...</p>
        </div>
      </div>
    );
  }

  if (!isAuthenticated) {
    return <Navigate to="/login" replace />;
  }

  return (
    <div className={`min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 text-white ${isPageLoaded ? 'animate-fade-in' : 'opacity-0'}`}>
      {/* Animated Background Elements */}
      <div className="fixed inset-0 pointer-events-none overflow-hidden">
        <div className="absolute top-20 left-20 w-32 h-32 bg-purple-500/10 rounded-full animate-float"></div>
        <div className="absolute bottom-20 right-20 w-24 h-24 bg-blue-500/10 rounded-full animate-float-delayed"></div>
        <div className="absolute top-1/2 left-10 w-16 h-16 bg-pink-500/10 rounded-full animate-float"></div>
        <div className="absolute bottom-1/3 left-1/3 w-20 h-20 bg-cyan-500/10 rounded-full animate-float-delayed"></div>
        
        {/* Floating particles */}
        {[...Array(15)].map((_, i) => (
          <div
            key={i}
            className="absolute w-2 h-2 bg-white/20 rounded-full animate-float"
            style={{
              left: `${Math.random() * 100}%`,
              top: `${Math.random() * 100}%`,
              animationDelay: `${Math.random() * 5}s`,
              animationDuration: `${3 + Math.random() * 4}s`
            }}
          />
        ))}
      </div>

      <div className="relative z-10 container mx-auto px-6 py-8">
        {/* Header */}
        <div className={`mb-12 text-center ${isPageLoaded ? 'animate-slide-in-top' : 'opacity-0'}`}>
          <h1 className="text-5xl font-bold mb-4 bg-gradient-to-r from-white via-purple-200 to-pink-200 bg-clip-text text-transparent animate-gradient">
            AI QA Dashboard
          </h1>
          <p className="text-xl text-gray-300 mb-2">
            Welcome back, <span className="text-purple-400 font-semibold">{user?.name}</span>
          </p>
          <Badge className="bg-gradient-to-r from-purple-600 to-pink-600 text-white px-4 py-2 text-sm">
            {user?.role?.charAt(0).toUpperCase() + user?.role?.slice(1)} Dashboard
          </Badge>
        </div>

        {/* Statistics Cards */}
        <div ref={statsRef} className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-12">
          {[
            { 
              title: 'Lines of Code Analyzed', 
              value: stats.linesAnalyzed, 
              suffix: '+', 
              icon: Code, 
              color: 'from-blue-500 to-cyan-500',
              trend: '+12%'
            },
            { 
              title: 'Programming Languages', 
              value: stats.languagesUsed, 
              suffix: '+', 
              icon: Globe, 
              color: 'from-green-500 to-emerald-500',
              trend: '+3'
            },
            { 
              title: 'AI APIs Supported', 
              value: stats.apisSupported, 
              suffix: '+', 
              icon: Database, 
              color: 'from-purple-500 to-pink-500',
              trend: 'Stable'
            },
            { 
              title: 'Accuracy Rate', 
              value: stats.accuracyRate, 
              suffix: '%', 
              icon: Target, 
              color: 'from-orange-500 to-red-500',
              trend: '+5%'
            }
          ].map((stat, index) => {
            const Icon = stat.icon;
            return (
              <Card 
                key={stat.title}
                className={`stat-card animate-on-scroll opacity-0 translate-y-10 transition-all duration-700 bg-white/10 backdrop-blur-lg border-white/20 hover:bg-white/20 hover:scale-105 hover:shadow-2xl hover:shadow-purple-500/20`}
                style={{transitionDelay: `${index * 100}ms`}}
              >
                <CardContent className="p-6">
                  <div className="flex items-center justify-between mb-4">
                    <div className={`p-3 rounded-xl bg-gradient-to-r ${stat.color}`}>
                      <Icon className="h-6 w-6 text-white" />
                    </div>
                    <div className="flex items-center text-sm text-green-400">
                      <ArrowUp className="h-4 w-4 mr-1" />
                      {stat.trend}
                    </div>
                  </div>
                  <div className="space-y-2">
                    <p className="text-sm text-gray-400">{stat.title}</p>
                    <p className="text-3xl font-bold text-white">
                      {stat.value.toLocaleString()}{stat.suffix}
                    </p>
                  </div>
                </CardContent>
              </Card>
            );
          })}
        </div>

        {/* Main Content Tabs */}
        <Tabs defaultValue="overview" className="space-y-8">
          <TabsList className="grid w-full grid-cols-4 bg-white/10 backdrop-blur-lg">
            <TabsTrigger value="overview" className="data-[state=active]:bg-purple-600">Overview</TabsTrigger>
            <TabsTrigger value="test-suites" className="data-[state=active]:bg-purple-600">Test Suites</TabsTrigger>
            <TabsTrigger value="bug-reports" className="data-[state=active]:bg-purple-600">Bug Reports</TabsTrigger>
            <TabsTrigger value="analytics" className="data-[state=active]:bg-purple-600">Analytics</TabsTrigger>
          </TabsList>

          <TabsContent value="overview" className="space-y-8">
            {/* Recent Activity */}
            <Card className="animate-on-scroll opacity-0 translate-y-10 transition-all duration-700 bg-white/10 backdrop-blur-lg border-white/20">
              <CardHeader>
                <CardTitle className="flex items-center gap-2 text-white">
                  <Activity className="h-5 w-5" />
                  Recent Activity
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {recentActivity.map((activity, index) => {
                    const Icon = activity.icon;
                    return (
                      <div key={activity.id} className="flex items-center gap-4 p-3 rounded-lg bg-white/5 hover:bg-white/10 transition-colors">
                        <div className="p-2 rounded-lg bg-purple-600/20">
                          <Icon className="h-4 w-4 text-purple-400" />
                        </div>
                        <div className="flex-1">
                          <p className="text-white font-medium">{activity.description}</p>
                          <p className="text-gray-400 text-sm">{activity.time}</p>
                        </div>
                      </div>
                    );
                  })}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="test-suites" className="space-y-6">
            <div className="flex items-center justify-between">
              <h2 className="text-2xl font-bold text-white">Test Suites</h2>
              <Button 
                onClick={() => window.location.href = '/test-suites'}
                className="bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700"
              >
                <Plus className="mr-2 h-4 w-4" />
                Generate New Tests
              </Button>
            </div>
            
            <div className="grid gap-6">
              {testResults.map((test, index) => (
                <Card key={index} className="report-card animate-on-scroll opacity-0 translate-y-10 transition-all duration-700 bg-white/10 backdrop-blur-lg border-white/20">
                  <CardHeader>
                    <div className="flex items-center justify-between">
                      <CardTitle className="text-white">{test.purpose || 'Test Suite'}</CardTitle>
                      <div className="flex gap-2">
                        <Badge className="bg-blue-600">{test.language}</Badge>
                        <Button 
                          size="sm" 
                          variant="outline"
                          onClick={() => exportToPDF('test-suite', test)}
                          className="border-white/20 text-white hover:bg-white/10"
                        >
                          <Download className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                    <CardDescription className="text-gray-300">
                      Generated {new Date(test.timestamp).toLocaleDateString()}
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <p className="text-gray-300 text-sm">
                      {typeof test.tests === 'string' ? test.tests.substring(0, 200) + '...' : 'Test suite generated successfully'}
                    </p>
                  </CardContent>
                </Card>
              ))}
            </div>
          </TabsContent>

          <TabsContent value="bug-reports" className="space-y-6">
            <div className="flex items-center justify-between">
              <h2 className="text-2xl font-bold text-white">Bug Reports</h2>
              <Button 
                onClick={() => window.location.href = '/bug-reports'}
                className="bg-gradient-to-r from-red-600 to-pink-600 hover:from-red-700 hover:to-pink-700"
              >
                <Plus className="mr-2 h-4 w-4" />
                Create Bug Report
              </Button>
            </div>
            
            <div className="grid gap-6">
              {bugReports.map((bug, index) => (
                <Card key={bug.id} className="report-card animate-on-scroll opacity-0 translate-y-10 transition-all duration-700 bg-white/10 backdrop-blur-lg border-white/20">
                  <CardHeader>
                    <div className="flex items-center justify-between">
                      <CardTitle className="text-white">{bug.title}</CardTitle>
                      <div className="flex gap-2">
                        <Badge className={`${
                          bug.severity === 'critical' ? 'bg-red-600' :
                          bug.severity === 'high' ? 'bg-orange-600' :
                          bug.severity === 'medium' ? 'bg-yellow-600' : 'bg-green-600'
                        }`}>
                          {bug.severity}
                        </Badge>
                        <Button 
                          size="sm" 
                          variant="outline"
                          onClick={() => exportToPDF('bug-report', bug)}
                          className="border-white/20 text-white hover:bg-white/10"
                        >
                          <Download className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                    <CardDescription className="text-gray-300">
                      Created {new Date(bug.createdAt).toLocaleDateString()}
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <p className="text-gray-300 text-sm">{bug.description.substring(0, 200)}...</p>
                  </CardContent>
                </Card>
              ))}
            </div>
          </TabsContent>

          <TabsContent value="analytics" className="space-y-6">
            <div className="grid lg:grid-cols-2 gap-6">
              <Card className="chart-card animate-on-scroll opacity-0 translate-y-10 transition-all duration-700 bg-white/10 backdrop-blur-lg border-white/20">
                <CardHeader>
                  <CardTitle className="text-white">Usage Analytics</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="h-64 flex items-center justify-center text-gray-400">
                    <BarChart3 className="h-16 w-16 mb-4" />
                    <p>Analytics chart will be implemented here</p>
                  </div>
                </CardContent>
              </Card>
              
              <Card className="chart-card animate-on-scroll opacity-0 translate-y-10 transition-all duration-700 bg-white/10 backdrop-blur-lg border-white/20">
                <CardHeader>
                  <CardTitle className="text-white">Performance Metrics</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="h-64 flex items-center justify-center text-gray-400">
                    <TrendingUp className="h-16 w-16 mb-4" />
                    <p>Performance metrics will be implemented here</p>
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
};

export default Dashboard;
