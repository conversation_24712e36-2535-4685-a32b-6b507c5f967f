class TestAutomationUI {
    constructor() {
        this.initializeElements();
        this.attachEventListeners();
        this.updateCharacterCounters();
    }

    initializeElements() {
        this.codeInput = document.getElementById('code-input');
        this.purposeInput = document.getElementById('purpose-input');
        this.analyzeBtn = document.getElementById('analyze-btn');
        this.generateTestsBtn = document.getElementById('generate-tests-btn');
        this.clearOutputBtn = document.getElementById('clear-output');
        this.output = document.getElementById('output');
        this.status = document.getElementById('status');
        this.timestamp = document.getElementById('timestamp');
        this.codeCounter = document.getElementById('code-counter');
        this.purposeCounter = document.getElementById('purpose-counter');
    }

    attachEventListeners() {
        this.analyzeBtn.addEventListener('click', () => this.analyzeCode());
        this.generateTestsBtn.addEventListener('click', () => this.generateTests());
        this.clearOutputBtn.addEventListener('click', () => this.clearOutput());

        this.codeInput.addEventListener('input', () => this.updateCharacterCounters());
        this.purposeInput.addEventListener('input', () => this.updateCharacterCounters());

        // Enable keyboard shortcuts
        document.addEventListener('keydown', (e) => {
            if (e.ctrlKey && e.key === 'Enter') {
                this.analyzeCode();
            }
        });
    }

    updateCharacterCounters() {
        this.codeCounter.textContent = this.codeInput.value.length;
        this.purposeCounter.textContent = this.purposeInput.value.length;

        // Update counter colors based on limits
        this.codeCounter.style.color = this.codeInput.value.length > 10240 ? '#e74c3c' : '#7f8c8d';
        this.purposeCounter.style.color = this.purposeInput.value.length > 1024 ? '#e74c3c' : '#7f8c8d';
    }

    validateInputs() {
        const code = this.codeInput.value.trim();
        const purpose = this.purposeInput.value.trim();

        if (!code) {
            this.showError('Code is required');
            return false;
        }

        if (!purpose) {
            this.showError('Purpose is required');
            return false;
        }

        if (code.length > 10240) {
            this.showError('Code exceeds maximum size of 10KB');
            return false;
        }

        if (purpose.length > 1024) {
            this.showError('Purpose exceeds maximum size of 1KB');
            return false;
        }

        return true;
    }

    async analyzeCode() {
        if (!this.validateInputs()) return;

        this.setLoading(this.analyzeBtn, true);
        this.updateStatus('Analyzing code...', 'warning');

        try {
            const response = await fetch('/api/analyze', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    code: this.codeInput.value,
                    purpose: this.purposeInput.value
                })
            });

            const result = await response.json();

            if (response.ok) {
                this.displayResult(result, 'Code Analysis Results');
                this.updateStatus('Analysis completed successfully', 'success');
            } else {
                this.showError(result.message || 'Analysis failed');
            }

        } catch (error) {
            this.showError('Network error: ' + error.message);
        } finally {
            this.setLoading(this.analyzeBtn, false);
        }
    }

    async generateTests() {
        if (!this.validateInputs()) return;

        this.setLoading(this.generateTestsBtn, true);
        this.updateStatus('Generating tests...', 'warning');

        try {
            const response = await fetch('/api/generate-tests', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    code: this.codeInput.value,
                    purpose: this.purposeInput.value
                })
            });

            const result = await response.json();

            if (response.ok) {
                this.displayResult(result, 'Test Generation Results');
                this.updateStatus('Test generation completed successfully', 'success');
            } else {
                this.showError(result.message || 'Test generation failed');
            }

        } catch (error) {
            this.showError('Network error: ' + error.message);
        } finally {
            this.setLoading(this.generateTestsBtn, false);
        }
    }

    setLoading(button, isLoading) {
        const textSpan = button.querySelector('.btn-text');
        const loadingSpan = button.querySelector('.btn-loading');

        if (isLoading) {
            textSpan.style.display = 'none';
            loadingSpan.style.display = 'inline';
            button.disabled = true;
        } else {
            textSpan.style.display = 'inline';
            loadingSpan.style.display = 'none';
            button.disabled = false;
        }
    }

    displayResult(result, title) {
        if (result.status === 'success' && result.data) {
            if (title.includes('Analysis')) {
                this.displayAnalysisResult(result.data, title);
            } else if (title.includes('Test')) {
                this.displayTestResult(result.data, title);
            } else {
                this.displayRawResult(result, title);
            }
        } else {
            this.displayRawResult(result, title);
        }
        this.updateTimestamp();
    }

    displayAnalysisResult(data, title) {
        let html = `<div class="result-container">`;
        html += `<h3>${title}</h3>`;

        // Security Analysis
        html += `<div class="analysis-section">`;
        html += `<div class="section-title">🔒 Security Analysis</div>`;
        html += `<div class="section-content">`;
        if (data.security && data.security.length > 0) {
            data.security.forEach((item, index) => {
                const icon = item.toLowerCase().includes('no') || item.toLowerCase().includes('found') ? '✅' : '⚠️';
                html += `<div class="analysis-item">${icon} ${item}</div>`;
            });
        } else {
            html += `<div class="analysis-item">No security analysis available</div>`;
        }
        html += `</div></div>`;

        // Performance Analysis
        html += `<div class="analysis-section">`;
        html += `<div class="section-title">⚡ Performance Analysis</div>`;
        html += `<div class="section-content">`;
        if (data.performance && data.performance.length > 0) {
            data.performance.forEach((item, index) => {
                const icon = item.toLowerCase().includes('no') || item.toLowerCase().includes('good') ? '✅' : '⚠️';
                html += `<div class="analysis-item">${icon} ${item}</div>`;
            });
        } else {
            html += `<div class="analysis-item">No performance analysis available</div>`;
        }
        html += `</div></div>`;

        // Optimization Suggestions
        html += `<div class="analysis-section">`;
        html += `<div class="section-title">🚀 Optimization Suggestions</div>`;
        html += `<div class="section-content">`;
        if (data.optimization && data.optimization.length > 0) {
            data.optimization.forEach((item, index) => {
                const icon = item.toLowerCase().includes('well') || item.toLowerCase().includes('good') ? '✅' : '💡';
                html += `<div class="analysis-item">${icon} ${item}</div>`;
            });
        } else {
            html += `<div class="analysis-item">No optimization suggestions available</div>`;
        }
        html += `</div></div>`;

        // Functionality Assessment
        html += `<div class="analysis-section">`;
        html += `<div class="section-title">✅ Functionality Assessment</div>`;
        html += `<div class="section-content">`;
        if (data.functionality && data.functionality.length > 0) {
            data.functionality.forEach((item, index) => {
                html += `<div class="analysis-item">📋 ${item}</div>`;
            });
        } else {
            html += `<div class="analysis-item">No functionality assessment available</div>`;
        }
        html += `</div></div>`;

        html += `</div>`;
        this.output.innerHTML = html;
    }

    displayTestResult(data, title) {
        let html = `<div class="result-container">`;
        html += `<h3>${title}</h3>`;

        // Generated Tests
        html += `<div class="test-section">`;
        html += `<div class="section-title">🧪 Generated Test Suite</div>`;
        if (data.tests) {
            html += `<div class="test-code"><pre>${this.escapeHtml(data.tests)}</pre></div>`;
        } else {
            html += `<div class="no-content">No tests generated</div>`;
        }
        html += `</div>`;

        // Code Fixes
        html += `<div class="fixes-section">`;
        html += `<div class="section-title">🔧 Suggested Fixes</div>`;
        if (data.fixes && data.fixes.length > 0) {
            data.fixes.forEach((fix, index) => {
                html += `<div class="fix-item">`;
                html += `<div class="fix-header">Fix #${index + 1}</div>`;
                html += `<div class="issue-description">Issue: ${this.escapeHtml(fix.issue)}</div>`;
                html += `<div class="fix-label">Fixed Code:</div>`;
                html += `<div class="fix-code"><pre>${this.escapeHtml(fix.fixedCode)}</pre></div>`;
                html += `</div>`;
            });
        } else {
            html += `<div class="no-fixes">✅ No fixes needed - code looks good!</div>`;
        }
        html += `</div>`;

        html += `</div>`;
        this.output.innerHTML = html;
    }

    displayRawResult(result, title) {
        const formattedResult = JSON.stringify(result, null, 2);
        this.output.textContent = `=== ${title} ===\n\n${formattedResult}`;
    }

    escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }



    showError(message) {
        this.output.innerHTML = `<div class="error-message">
            <div class="error-header">❌ Error</div>
            <div class="error-content">${this.escapeHtml(message)}</div>
        </div>`;
        this.updateStatus(message, 'error');
        this.updateTimestamp();
    }

    clearOutput() {
        this.output.innerHTML = '<div class="no-content">Results will appear here...</div>';
        this.updateStatus('Ready', '');
        this.timestamp.textContent = '';
    }

    updateStatus(message, type = '') {
        this.status.textContent = message;
        this.status.className = type;
    }

    updateTimestamp() {
        this.timestamp.textContent = new Date().toLocaleString();
    }
}

// Initialize the application when the DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    new TestAutomationUI();
});
