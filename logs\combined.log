{"level":"info","message":"Test Automation System running on port 3000","service":"test-automation-system","timestamp":"2025-06-08T12:51:37.890Z"}
{"level":"info","message":"Environment: development","service":"test-automation-system","timestamp":"2025-06-08T12:51:37.901Z"}
{"level":"info","message":"Test Automation System running on port 3001","service":"test-automation-system","timestamp":"2025-06-08T12:52:18.610Z"}
{"level":"info","message":"Environment: development","service":"test-automation-system","timestamp":"2025-06-08T12:52:18.618Z"}
{"level":"info","message":"GET /health - ::1","service":"test-automation-system","timestamp":"2025-06-08T12:52:43.213Z"}
{"level":"info","message":"POST /api/analyze - ::1","service":"test-automation-system","timestamp":"2025-06-08T12:53:12.153Z"}
{"codeLength":36,"level":"info","message":"Starting code analysis","purposeLength":15,"service":"test-automation-system","timestamp":"2025-06-08T12:53:12.156Z"}
{"level":"info","message":"Code analysis completed successfully","service":"test-automation-system","timestamp":"2025-06-08T12:53:19.354Z"}
{"level":"info","message":"GET / - ::1","service":"test-automation-system","timestamp":"2025-06-08T12:53:24.898Z"}
{"level":"info","message":"GET /script.js - ::1","service":"test-automation-system","timestamp":"2025-06-08T12:53:25.578Z"}
{"level":"info","message":"GET /styles.css - ::1","service":"test-automation-system","timestamp":"2025-06-08T12:53:25.580Z"}
{"level":"info","message":"GET /favicon.ico - ::1","service":"test-automation-system","timestamp":"2025-06-08T12:53:25.674Z"}
{"level":"info","message":"GET /health - ::1","service":"test-automation-system","timestamp":"2025-06-08T12:55:09.406Z"}
{"level":"info","message":"POST /api/analyze - ::1","service":"test-automation-system","timestamp":"2025-06-08T12:55:09.419Z"}
{"codeLength":36,"level":"info","message":"Starting code analysis","purposeLength":15,"service":"test-automation-system","timestamp":"2025-06-08T12:55:09.421Z"}
{"level":"info","message":"Code analysis completed successfully","service":"test-automation-system","timestamp":"2025-06-08T12:55:15.971Z"}
{"level":"info","message":"POST /api/generate-tests - ::1","service":"test-automation-system","timestamp":"2025-06-08T12:55:16.000Z"}
{"codeLength":41,"level":"info","message":"Starting test generation","purposeLength":20,"service":"test-automation-system","timestamp":"2025-06-08T12:55:16.002Z"}
{"level":"info","message":"Test generation completed successfully","service":"test-automation-system","timestamp":"2025-06-08T12:55:54.775Z"}
{"level":"info","message":"POST /api/analyze - ::1","service":"test-automation-system","timestamp":"2025-06-08T12:55:54.783Z"}
{"level":"error","message":"Analysis error: Code is required and must be a string","service":"test-automation-system","stack":"Error: Code is required and must be a string\n    at Validator.validateCode (C:\\Users\\<USER>\\Desktop\\MVP\\utils\\validator.js:6:13)\n    at analyzeCode (C:\\Users\\<USER>\\Desktop\\MVP\\controllers\\analysisController.js:11:39)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\MVP\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\Desktop\\MVP\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (C:\\Users\\<USER>\\Desktop\\MVP\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\MVP\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at C:\\Users\\<USER>\\Desktop\\MVP\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (C:\\Users\\<USER>\\Desktop\\MVP\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Users\\<USER>\\Desktop\\MVP\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\Users\\<USER>\\Desktop\\MVP\\node_modules\\express\\lib\\router\\index.js:175:3)","timestamp":"2025-06-08T12:55:54.784Z"}
{"error":"Code is required and must be a string","ip":"::1","level":"error","message":"Error occurred:","method":"POST","service":"test-automation-system","stack":"Error: Code is required and must be a string\n    at Validator.validateCode (C:\\Users\\<USER>\\Desktop\\MVP\\utils\\validator.js:6:13)\n    at analyzeCode (C:\\Users\\<USER>\\Desktop\\MVP\\controllers\\analysisController.js:11:39)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\MVP\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\Desktop\\MVP\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (C:\\Users\\<USER>\\Desktop\\MVP\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\MVP\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at C:\\Users\\<USER>\\Desktop\\MVP\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (C:\\Users\\<USER>\\Desktop\\MVP\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Users\\<USER>\\Desktop\\MVP\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\Users\\<USER>\\Desktop\\MVP\\node_modules\\express\\lib\\router\\index.js:175:3)","timestamp":"2025-06-08T12:55:54.786Z","url":"/api/analyze"}
{"level":"info","message":"GET /nonexistent - ::1","service":"test-automation-system","timestamp":"2025-06-08T12:55:54.829Z"}
{"level":"info","message":"POST /api/analyze - ::1","service":"test-automation-system","timestamp":"2025-06-08T12:58:03.861Z"}
{"codeLength":180,"level":"info","message":"Starting code analysis","purposeLength":20,"service":"test-automation-system","timestamp":"2025-06-08T12:58:03.862Z"}
{"level":"info","message":"Code analysis completed successfully","service":"test-automation-system","timestamp":"2025-06-08T12:58:14.765Z"}
{"level":"info","message":"POST /api/generate-tests - ::1","service":"test-automation-system","timestamp":"2025-06-08T12:58:26.328Z"}
{"codeLength":180,"level":"info","message":"Starting test generation","purposeLength":20,"service":"test-automation-system","timestamp":"2025-06-08T12:58:26.331Z"}
{"level":"info","message":"Test generation completed successfully","service":"test-automation-system","timestamp":"2025-06-08T12:59:15.085Z"}
{"level":"info","message":"GET /demo-formatting.html - ::1","service":"test-automation-system","timestamp":"2025-06-08T13:03:45.393Z"}
{"level":"info","message":"POST /api/analyze - ::1","service":"test-automation-system","timestamp":"2025-06-08T13:05:16.852Z"}
{"codeLength":180,"level":"info","message":"Starting code analysis","purposeLength":20,"service":"test-automation-system","timestamp":"2025-06-08T13:05:16.853Z"}
{"level":"info","message":"Code analysis completed successfully","service":"test-automation-system","timestamp":"2025-06-08T13:05:27.403Z"}
{"level":"info","message":"POST /api/generate-tests - ::1","service":"test-automation-system","timestamp":"2025-06-08T13:05:33.182Z"}
{"codeLength":180,"level":"info","message":"Starting test generation","purposeLength":20,"service":"test-automation-system","timestamp":"2025-06-08T13:05:33.183Z"}
{"level":"info","message":"Test generation completed successfully","service":"test-automation-system","timestamp":"2025-06-08T13:06:03.996Z"}
