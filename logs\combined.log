{"level":"info","message":"Test Automation System running on port 3000","service":"test-automation-system","timestamp":"2025-06-08T12:51:37.890Z"}
{"level":"info","message":"Environment: development","service":"test-automation-system","timestamp":"2025-06-08T12:51:37.901Z"}
{"level":"info","message":"Test Automation System running on port 3001","service":"test-automation-system","timestamp":"2025-06-08T12:52:18.610Z"}
{"level":"info","message":"Environment: development","service":"test-automation-system","timestamp":"2025-06-08T12:52:18.618Z"}
{"level":"info","message":"GET /health - ::1","service":"test-automation-system","timestamp":"2025-06-08T12:52:43.213Z"}
{"level":"info","message":"POST /api/analyze - ::1","service":"test-automation-system","timestamp":"2025-06-08T12:53:12.153Z"}
{"codeLength":36,"level":"info","message":"Starting code analysis","purposeLength":15,"service":"test-automation-system","timestamp":"2025-06-08T12:53:12.156Z"}
{"level":"info","message":"Code analysis completed successfully","service":"test-automation-system","timestamp":"2025-06-08T12:53:19.354Z"}
{"level":"info","message":"GET / - ::1","service":"test-automation-system","timestamp":"2025-06-08T12:53:24.898Z"}
{"level":"info","message":"GET /script.js - ::1","service":"test-automation-system","timestamp":"2025-06-08T12:53:25.578Z"}
{"level":"info","message":"GET /styles.css - ::1","service":"test-automation-system","timestamp":"2025-06-08T12:53:25.580Z"}
{"level":"info","message":"GET /favicon.ico - ::1","service":"test-automation-system","timestamp":"2025-06-08T12:53:25.674Z"}
{"level":"info","message":"GET /health - ::1","service":"test-automation-system","timestamp":"2025-06-08T12:55:09.406Z"}
{"level":"info","message":"POST /api/analyze - ::1","service":"test-automation-system","timestamp":"2025-06-08T12:55:09.419Z"}
{"codeLength":36,"level":"info","message":"Starting code analysis","purposeLength":15,"service":"test-automation-system","timestamp":"2025-06-08T12:55:09.421Z"}
{"level":"info","message":"Code analysis completed successfully","service":"test-automation-system","timestamp":"2025-06-08T12:55:15.971Z"}
{"level":"info","message":"POST /api/generate-tests - ::1","service":"test-automation-system","timestamp":"2025-06-08T12:55:16.000Z"}
{"codeLength":41,"level":"info","message":"Starting test generation","purposeLength":20,"service":"test-automation-system","timestamp":"2025-06-08T12:55:16.002Z"}
{"level":"info","message":"Test generation completed successfully","service":"test-automation-system","timestamp":"2025-06-08T12:55:54.775Z"}
{"level":"info","message":"POST /api/analyze - ::1","service":"test-automation-system","timestamp":"2025-06-08T12:55:54.783Z"}
{"level":"error","message":"Analysis error: Code is required and must be a string","service":"test-automation-system","stack":"Error: Code is required and must be a string\n    at Validator.validateCode (C:\\Users\\<USER>\\Desktop\\MVP\\utils\\validator.js:6:13)\n    at analyzeCode (C:\\Users\\<USER>\\Desktop\\MVP\\controllers\\analysisController.js:11:39)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\MVP\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\Desktop\\MVP\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (C:\\Users\\<USER>\\Desktop\\MVP\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\MVP\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at C:\\Users\\<USER>\\Desktop\\MVP\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (C:\\Users\\<USER>\\Desktop\\MVP\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Users\\<USER>\\Desktop\\MVP\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\Users\\<USER>\\Desktop\\MVP\\node_modules\\express\\lib\\router\\index.js:175:3)","timestamp":"2025-06-08T12:55:54.784Z"}
{"error":"Code is required and must be a string","ip":"::1","level":"error","message":"Error occurred:","method":"POST","service":"test-automation-system","stack":"Error: Code is required and must be a string\n    at Validator.validateCode (C:\\Users\\<USER>\\Desktop\\MVP\\utils\\validator.js:6:13)\n    at analyzeCode (C:\\Users\\<USER>\\Desktop\\MVP\\controllers\\analysisController.js:11:39)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\MVP\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\Desktop\\MVP\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (C:\\Users\\<USER>\\Desktop\\MVP\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\MVP\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at C:\\Users\\<USER>\\Desktop\\MVP\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (C:\\Users\\<USER>\\Desktop\\MVP\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Users\\<USER>\\Desktop\\MVP\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\Users\\<USER>\\Desktop\\MVP\\node_modules\\express\\lib\\router\\index.js:175:3)","timestamp":"2025-06-08T12:55:54.786Z","url":"/api/analyze"}
{"level":"info","message":"GET /nonexistent - ::1","service":"test-automation-system","timestamp":"2025-06-08T12:55:54.829Z"}
{"level":"info","message":"POST /api/analyze - ::1","service":"test-automation-system","timestamp":"2025-06-08T12:58:03.861Z"}
{"codeLength":180,"level":"info","message":"Starting code analysis","purposeLength":20,"service":"test-automation-system","timestamp":"2025-06-08T12:58:03.862Z"}
{"level":"info","message":"Code analysis completed successfully","service":"test-automation-system","timestamp":"2025-06-08T12:58:14.765Z"}
{"level":"info","message":"POST /api/generate-tests - ::1","service":"test-automation-system","timestamp":"2025-06-08T12:58:26.328Z"}
{"codeLength":180,"level":"info","message":"Starting test generation","purposeLength":20,"service":"test-automation-system","timestamp":"2025-06-08T12:58:26.331Z"}
{"level":"info","message":"Test generation completed successfully","service":"test-automation-system","timestamp":"2025-06-08T12:59:15.085Z"}
{"level":"info","message":"GET /demo-formatting.html - ::1","service":"test-automation-system","timestamp":"2025-06-08T13:03:45.393Z"}
{"level":"info","message":"POST /api/analyze - ::1","service":"test-automation-system","timestamp":"2025-06-08T13:05:16.852Z"}
{"codeLength":180,"level":"info","message":"Starting code analysis","purposeLength":20,"service":"test-automation-system","timestamp":"2025-06-08T13:05:16.853Z"}
{"level":"info","message":"Code analysis completed successfully","service":"test-automation-system","timestamp":"2025-06-08T13:05:27.403Z"}
{"level":"info","message":"POST /api/generate-tests - ::1","service":"test-automation-system","timestamp":"2025-06-08T13:05:33.182Z"}
{"codeLength":180,"level":"info","message":"Starting test generation","purposeLength":20,"service":"test-automation-system","timestamp":"2025-06-08T13:05:33.183Z"}
{"level":"info","message":"Test generation completed successfully","service":"test-automation-system","timestamp":"2025-06-08T13:06:03.996Z"}
{"level":"info","message":"POST /api/analyze - ::1","service":"test-automation-system","timestamp":"2025-06-08T14:11:45.251Z"}
{"codeLength":180,"level":"info","message":"Starting code analysis","purposeLength":20,"service":"test-automation-system","timestamp":"2025-06-08T14:11:45.255Z"}
{"level":"info","message":"Code analysis completed successfully","service":"test-automation-system","timestamp":"2025-06-08T14:11:55.584Z"}
{"level":"info","message":"POST /api/analyze - ::1","service":"test-automation-system","timestamp":"2025-06-08T14:14:02.154Z"}
{"level":"error","message":"Analysis error: Code contains potentially malicious patterns","service":"test-automation-system","stack":"Error: Code contains potentially malicious patterns\n    at Validator.validateCode (C:\\Users\\<USER>\\Desktop\\MVP\\utils\\validator.js:15:13)\n    at analyzeCode (C:\\Users\\<USER>\\Desktop\\MVP\\controllers\\analysisController.js:11:39)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\MVP\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\Desktop\\MVP\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (C:\\Users\\<USER>\\Desktop\\MVP\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\MVP\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at C:\\Users\\<USER>\\Desktop\\MVP\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (C:\\Users\\<USER>\\Desktop\\MVP\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Users\\<USER>\\Desktop\\MVP\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\Users\\<USER>\\Desktop\\MVP\\node_modules\\express\\lib\\router\\index.js:175:3)","timestamp":"2025-06-08T14:14:02.156Z"}
{"error":"Code contains potentially malicious patterns","ip":"::1","level":"error","message":"Error occurred:","method":"POST","service":"test-automation-system","stack":"Error: Code contains potentially malicious patterns\n    at Validator.validateCode (C:\\Users\\<USER>\\Desktop\\MVP\\utils\\validator.js:15:13)\n    at analyzeCode (C:\\Users\\<USER>\\Desktop\\MVP\\controllers\\analysisController.js:11:39)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\MVP\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\Desktop\\MVP\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (C:\\Users\\<USER>\\Desktop\\MVP\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\MVP\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at C:\\Users\\<USER>\\Desktop\\MVP\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (C:\\Users\\<USER>\\Desktop\\MVP\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Users\\<USER>\\Desktop\\MVP\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\Users\\<USER>\\Desktop\\MVP\\node_modules\\express\\lib\\router\\index.js:175:3)","timestamp":"2025-06-08T14:14:02.157Z","url":"/api/analyze","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"POST /api/generate-tests - ::1","service":"test-automation-system","timestamp":"2025-06-08T14:14:11.219Z"}
{"level":"error","message":"Test generation error: Code contains potentially malicious patterns","service":"test-automation-system","stack":"Error: Code contains potentially malicious patterns\n    at Validator.validateCode (C:\\Users\\<USER>\\Desktop\\MVP\\utils\\validator.js:15:13)\n    at generateTests (C:\\Users\\<USER>\\Desktop\\MVP\\controllers\\analysisController.js:47:39)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\MVP\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\Desktop\\MVP\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (C:\\Users\\<USER>\\Desktop\\MVP\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\MVP\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at C:\\Users\\<USER>\\Desktop\\MVP\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (C:\\Users\\<USER>\\Desktop\\MVP\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Users\\<USER>\\Desktop\\MVP\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\Users\\<USER>\\Desktop\\MVP\\node_modules\\express\\lib\\router\\index.js:175:3)","timestamp":"2025-06-08T14:14:11.220Z"}
{"error":"Code contains potentially malicious patterns","ip":"::1","level":"error","message":"Error occurred:","method":"POST","service":"test-automation-system","stack":"Error: Code contains potentially malicious patterns\n    at Validator.validateCode (C:\\Users\\<USER>\\Desktop\\MVP\\utils\\validator.js:15:13)\n    at generateTests (C:\\Users\\<USER>\\Desktop\\MVP\\controllers\\analysisController.js:47:39)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\MVP\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\Desktop\\MVP\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (C:\\Users\\<USER>\\Desktop\\MVP\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\MVP\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at C:\\Users\\<USER>\\Desktop\\MVP\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (C:\\Users\\<USER>\\Desktop\\MVP\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Users\\<USER>\\Desktop\\MVP\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\Users\\<USER>\\Desktop\\MVP\\node_modules\\express\\lib\\router\\index.js:175:3)","timestamp":"2025-06-08T14:14:11.221Z","url":"/api/generate-tests","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"POST /api/generate-tests - ::1","service":"test-automation-system","timestamp":"2025-06-08T14:14:15.703Z"}
{"level":"error","message":"Test generation error: Code contains potentially malicious patterns","service":"test-automation-system","stack":"Error: Code contains potentially malicious patterns\n    at Validator.validateCode (C:\\Users\\<USER>\\Desktop\\MVP\\utils\\validator.js:15:13)\n    at generateTests (C:\\Users\\<USER>\\Desktop\\MVP\\controllers\\analysisController.js:47:39)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\MVP\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\Desktop\\MVP\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (C:\\Users\\<USER>\\Desktop\\MVP\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\MVP\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at C:\\Users\\<USER>\\Desktop\\MVP\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (C:\\Users\\<USER>\\Desktop\\MVP\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Users\\<USER>\\Desktop\\MVP\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\Users\\<USER>\\Desktop\\MVP\\node_modules\\express\\lib\\router\\index.js:175:3)","timestamp":"2025-06-08T14:14:15.704Z"}
{"error":"Code contains potentially malicious patterns","ip":"::1","level":"error","message":"Error occurred:","method":"POST","service":"test-automation-system","stack":"Error: Code contains potentially malicious patterns\n    at Validator.validateCode (C:\\Users\\<USER>\\Desktop\\MVP\\utils\\validator.js:15:13)\n    at generateTests (C:\\Users\\<USER>\\Desktop\\MVP\\controllers\\analysisController.js:47:39)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\MVP\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\Desktop\\MVP\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (C:\\Users\\<USER>\\Desktop\\MVP\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\MVP\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at C:\\Users\\<USER>\\Desktop\\MVP\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (C:\\Users\\<USER>\\Desktop\\MVP\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Users\\<USER>\\Desktop\\MVP\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\Users\\<USER>\\Desktop\\MVP\\node_modules\\express\\lib\\router\\index.js:175:3)","timestamp":"2025-06-08T14:14:15.705Z","url":"/api/generate-tests","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"POST /api/generate-tests - ::1","service":"test-automation-system","timestamp":"2025-06-08T14:14:16.353Z"}
{"level":"error","message":"Test generation error: Code contains potentially malicious patterns","service":"test-automation-system","stack":"Error: Code contains potentially malicious patterns\n    at Validator.validateCode (C:\\Users\\<USER>\\Desktop\\MVP\\utils\\validator.js:15:13)\n    at generateTests (C:\\Users\\<USER>\\Desktop\\MVP\\controllers\\analysisController.js:47:39)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\MVP\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\Desktop\\MVP\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (C:\\Users\\<USER>\\Desktop\\MVP\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\MVP\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at C:\\Users\\<USER>\\Desktop\\MVP\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (C:\\Users\\<USER>\\Desktop\\MVP\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Users\\<USER>\\Desktop\\MVP\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\Users\\<USER>\\Desktop\\MVP\\node_modules\\express\\lib\\router\\index.js:175:3)","timestamp":"2025-06-08T14:14:16.354Z"}
{"error":"Code contains potentially malicious patterns","ip":"::1","level":"error","message":"Error occurred:","method":"POST","service":"test-automation-system","stack":"Error: Code contains potentially malicious patterns\n    at Validator.validateCode (C:\\Users\\<USER>\\Desktop\\MVP\\utils\\validator.js:15:13)\n    at generateTests (C:\\Users\\<USER>\\Desktop\\MVP\\controllers\\analysisController.js:47:39)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\MVP\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\Desktop\\MVP\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (C:\\Users\\<USER>\\Desktop\\MVP\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\MVP\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at C:\\Users\\<USER>\\Desktop\\MVP\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (C:\\Users\\<USER>\\Desktop\\MVP\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Users\\<USER>\\Desktop\\MVP\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\Users\\<USER>\\Desktop\\MVP\\node_modules\\express\\lib\\router\\index.js:175:3)","timestamp":"2025-06-08T14:14:16.354Z","url":"/api/generate-tests","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"POST /api/generate-tests - ::1","service":"test-automation-system","timestamp":"2025-06-08T14:14:16.778Z"}
{"level":"error","message":"Test generation error: Code contains potentially malicious patterns","service":"test-automation-system","stack":"Error: Code contains potentially malicious patterns\n    at Validator.validateCode (C:\\Users\\<USER>\\Desktop\\MVP\\utils\\validator.js:15:13)\n    at generateTests (C:\\Users\\<USER>\\Desktop\\MVP\\controllers\\analysisController.js:47:39)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\MVP\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\Desktop\\MVP\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (C:\\Users\\<USER>\\Desktop\\MVP\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\MVP\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at C:\\Users\\<USER>\\Desktop\\MVP\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (C:\\Users\\<USER>\\Desktop\\MVP\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Users\\<USER>\\Desktop\\MVP\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\Users\\<USER>\\Desktop\\MVP\\node_modules\\express\\lib\\router\\index.js:175:3)","timestamp":"2025-06-08T14:14:16.780Z"}
{"error":"Code contains potentially malicious patterns","ip":"::1","level":"error","message":"Error occurred:","method":"POST","service":"test-automation-system","stack":"Error: Code contains potentially malicious patterns\n    at Validator.validateCode (C:\\Users\\<USER>\\Desktop\\MVP\\utils\\validator.js:15:13)\n    at generateTests (C:\\Users\\<USER>\\Desktop\\MVP\\controllers\\analysisController.js:47:39)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\MVP\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\Desktop\\MVP\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (C:\\Users\\<USER>\\Desktop\\MVP\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\MVP\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at C:\\Users\\<USER>\\Desktop\\MVP\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (C:\\Users\\<USER>\\Desktop\\MVP\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Users\\<USER>\\Desktop\\MVP\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\Users\\<USER>\\Desktop\\MVP\\node_modules\\express\\lib\\router\\index.js:175:3)","timestamp":"2025-06-08T14:14:16.781Z","url":"/api/generate-tests","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"POST /api/analyze - ::1","service":"test-automation-system","timestamp":"2025-06-08T14:14:17.714Z"}
{"level":"error","message":"Analysis error: Code contains potentially malicious patterns","service":"test-automation-system","stack":"Error: Code contains potentially malicious patterns\n    at Validator.validateCode (C:\\Users\\<USER>\\Desktop\\MVP\\utils\\validator.js:15:13)\n    at analyzeCode (C:\\Users\\<USER>\\Desktop\\MVP\\controllers\\analysisController.js:11:39)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\MVP\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\Desktop\\MVP\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (C:\\Users\\<USER>\\Desktop\\MVP\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\MVP\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at C:\\Users\\<USER>\\Desktop\\MVP\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (C:\\Users\\<USER>\\Desktop\\MVP\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Users\\<USER>\\Desktop\\MVP\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\Users\\<USER>\\Desktop\\MVP\\node_modules\\express\\lib\\router\\index.js:175:3)","timestamp":"2025-06-08T14:14:17.716Z"}
{"error":"Code contains potentially malicious patterns","ip":"::1","level":"error","message":"Error occurred:","method":"POST","service":"test-automation-system","stack":"Error: Code contains potentially malicious patterns\n    at Validator.validateCode (C:\\Users\\<USER>\\Desktop\\MVP\\utils\\validator.js:15:13)\n    at analyzeCode (C:\\Users\\<USER>\\Desktop\\MVP\\controllers\\analysisController.js:11:39)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\MVP\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\Desktop\\MVP\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (C:\\Users\\<USER>\\Desktop\\MVP\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\MVP\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at C:\\Users\\<USER>\\Desktop\\MVP\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (C:\\Users\\<USER>\\Desktop\\MVP\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Users\\<USER>\\Desktop\\MVP\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\Users\\<USER>\\Desktop\\MVP\\node_modules\\express\\lib\\router\\index.js:175:3)","timestamp":"2025-06-08T14:14:17.716Z","url":"/api/analyze","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"GET /demo-formatting.html - ::1","service":"test-automation-system","timestamp":"2025-06-08T14:19:08.988Z"}
{"level":"info","message":"GET /demo-formatting.html - ::1","service":"test-automation-system","timestamp":"2025-06-08T14:21:00.087Z"}
{"level":"info","message":"GET /.well-known/appspecific/com.chrome.devtools.json - ::1","service":"test-automation-system","timestamp":"2025-06-08T14:21:04.243Z"}
{"level":"info","message":"POST /api/analyze - ::1","service":"test-automation-system","timestamp":"2025-06-08T14:21:46.933Z"}
{"level":"error","message":"Analysis error: Code contains potentially malicious patterns","service":"test-automation-system","stack":"Error: Code contains potentially malicious patterns\n    at Validator.validateCode (C:\\Users\\<USER>\\Desktop\\MVP\\utils\\validator.js:15:13)\n    at analyzeCode (C:\\Users\\<USER>\\Desktop\\MVP\\controllers\\analysisController.js:11:39)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\MVP\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\Desktop\\MVP\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (C:\\Users\\<USER>\\Desktop\\MVP\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\MVP\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at C:\\Users\\<USER>\\Desktop\\MVP\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (C:\\Users\\<USER>\\Desktop\\MVP\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Users\\<USER>\\Desktop\\MVP\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\Users\\<USER>\\Desktop\\MVP\\node_modules\\express\\lib\\router\\index.js:175:3)","timestamp":"2025-06-08T14:21:46.935Z"}
{"error":"Code contains potentially malicious patterns","ip":"::1","level":"error","message":"Error occurred:","method":"POST","service":"test-automation-system","stack":"Error: Code contains potentially malicious patterns\n    at Validator.validateCode (C:\\Users\\<USER>\\Desktop\\MVP\\utils\\validator.js:15:13)\n    at analyzeCode (C:\\Users\\<USER>\\Desktop\\MVP\\controllers\\analysisController.js:11:39)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\MVP\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\Desktop\\MVP\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (C:\\Users\\<USER>\\Desktop\\MVP\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\MVP\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at C:\\Users\\<USER>\\Desktop\\MVP\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (C:\\Users\\<USER>\\Desktop\\MVP\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Users\\<USER>\\Desktop\\MVP\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\Users\\<USER>\\Desktop\\MVP\\node_modules\\express\\lib\\router\\index.js:175:3)","timestamp":"2025-06-08T14:21:46.936Z","url":"/api/analyze","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"POST /api/analyze - ::1","service":"test-automation-system","timestamp":"2025-06-08T14:21:47.772Z"}
{"level":"error","message":"Analysis error: Code contains potentially malicious patterns","service":"test-automation-system","stack":"Error: Code contains potentially malicious patterns\n    at Validator.validateCode (C:\\Users\\<USER>\\Desktop\\MVP\\utils\\validator.js:15:13)\n    at analyzeCode (C:\\Users\\<USER>\\Desktop\\MVP\\controllers\\analysisController.js:11:39)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\MVP\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\Desktop\\MVP\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (C:\\Users\\<USER>\\Desktop\\MVP\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\MVP\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at C:\\Users\\<USER>\\Desktop\\MVP\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (C:\\Users\\<USER>\\Desktop\\MVP\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Users\\<USER>\\Desktop\\MVP\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\Users\\<USER>\\Desktop\\MVP\\node_modules\\express\\lib\\router\\index.js:175:3)","timestamp":"2025-06-08T14:21:47.774Z"}
{"error":"Code contains potentially malicious patterns","ip":"::1","level":"error","message":"Error occurred:","method":"POST","service":"test-automation-system","stack":"Error: Code contains potentially malicious patterns\n    at Validator.validateCode (C:\\Users\\<USER>\\Desktop\\MVP\\utils\\validator.js:15:13)\n    at analyzeCode (C:\\Users\\<USER>\\Desktop\\MVP\\controllers\\analysisController.js:11:39)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\MVP\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\Desktop\\MVP\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (C:\\Users\\<USER>\\Desktop\\MVP\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\MVP\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at C:\\Users\\<USER>\\Desktop\\MVP\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (C:\\Users\\<USER>\\Desktop\\MVP\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Users\\<USER>\\Desktop\\MVP\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\Users\\<USER>\\Desktop\\MVP\\node_modules\\express\\lib\\router\\index.js:175:3)","timestamp":"2025-06-08T14:21:47.775Z","url":"/api/analyze","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"GET / - ::1","service":"test-automation-system","timestamp":"2025-06-08T14:21:50.576Z"}
{"level":"info","message":"GET /styles.css - ::1","service":"test-automation-system","timestamp":"2025-06-08T14:21:50.627Z"}
{"level":"info","message":"GET /script.js - ::1","service":"test-automation-system","timestamp":"2025-06-08T14:21:50.633Z"}
{"level":"info","message":"POST /api/analyze - ::1","service":"test-automation-system","timestamp":"2025-06-08T14:22:09.865Z"}
{"level":"error","message":"Analysis error: Code contains potentially malicious patterns","service":"test-automation-system","stack":"Error: Code contains potentially malicious patterns\n    at Validator.validateCode (C:\\Users\\<USER>\\Desktop\\MVP\\utils\\validator.js:15:13)\n    at analyzeCode (C:\\Users\\<USER>\\Desktop\\MVP\\controllers\\analysisController.js:11:39)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\MVP\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\Desktop\\MVP\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (C:\\Users\\<USER>\\Desktop\\MVP\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\MVP\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at C:\\Users\\<USER>\\Desktop\\MVP\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (C:\\Users\\<USER>\\Desktop\\MVP\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Users\\<USER>\\Desktop\\MVP\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\Users\\<USER>\\Desktop\\MVP\\node_modules\\express\\lib\\router\\index.js:175:3)","timestamp":"2025-06-08T14:22:09.866Z"}
{"error":"Code contains potentially malicious patterns","ip":"::1","level":"error","message":"Error occurred:","method":"POST","service":"test-automation-system","stack":"Error: Code contains potentially malicious patterns\n    at Validator.validateCode (C:\\Users\\<USER>\\Desktop\\MVP\\utils\\validator.js:15:13)\n    at analyzeCode (C:\\Users\\<USER>\\Desktop\\MVP\\controllers\\analysisController.js:11:39)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\MVP\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\Desktop\\MVP\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (C:\\Users\\<USER>\\Desktop\\MVP\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\MVP\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at C:\\Users\\<USER>\\Desktop\\MVP\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (C:\\Users\\<USER>\\Desktop\\MVP\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Users\\<USER>\\Desktop\\MVP\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\Users\\<USER>\\Desktop\\MVP\\node_modules\\express\\lib\\router\\index.js:175:3)","timestamp":"2025-06-08T14:22:09.867Z","url":"/api/analyze","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"POST /api/generate-tests - ::1","service":"test-automation-system","timestamp":"2025-06-08T14:22:23.291Z"}
{"level":"error","message":"Test generation error: Code contains potentially malicious patterns","service":"test-automation-system","stack":"Error: Code contains potentially malicious patterns\n    at Validator.validateCode (C:\\Users\\<USER>\\Desktop\\MVP\\utils\\validator.js:15:13)\n    at generateTests (C:\\Users\\<USER>\\Desktop\\MVP\\controllers\\analysisController.js:47:39)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\MVP\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\Desktop\\MVP\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (C:\\Users\\<USER>\\Desktop\\MVP\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\MVP\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at C:\\Users\\<USER>\\Desktop\\MVP\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (C:\\Users\\<USER>\\Desktop\\MVP\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Users\\<USER>\\Desktop\\MVP\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\Users\\<USER>\\Desktop\\MVP\\node_modules\\express\\lib\\router\\index.js:175:3)","timestamp":"2025-06-08T14:22:23.292Z"}
{"error":"Code contains potentially malicious patterns","ip":"::1","level":"error","message":"Error occurred:","method":"POST","service":"test-automation-system","stack":"Error: Code contains potentially malicious patterns\n    at Validator.validateCode (C:\\Users\\<USER>\\Desktop\\MVP\\utils\\validator.js:15:13)\n    at generateTests (C:\\Users\\<USER>\\Desktop\\MVP\\controllers\\analysisController.js:47:39)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\MVP\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\Desktop\\MVP\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (C:\\Users\\<USER>\\Desktop\\MVP\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\MVP\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at C:\\Users\\<USER>\\Desktop\\MVP\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (C:\\Users\\<USER>\\Desktop\\MVP\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Users\\<USER>\\Desktop\\MVP\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\Users\\<USER>\\Desktop\\MVP\\node_modules\\express\\lib\\router\\index.js:175:3)","timestamp":"2025-06-08T14:22:23.293Z","url":"/api/generate-tests","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"POST /api/generate-tests - ::1","service":"test-automation-system","timestamp":"2025-06-08T14:22:23.974Z"}
{"level":"error","message":"Test generation error: Code contains potentially malicious patterns","service":"test-automation-system","stack":"Error: Code contains potentially malicious patterns\n    at Validator.validateCode (C:\\Users\\<USER>\\Desktop\\MVP\\utils\\validator.js:15:13)\n    at generateTests (C:\\Users\\<USER>\\Desktop\\MVP\\controllers\\analysisController.js:47:39)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\MVP\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\Desktop\\MVP\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (C:\\Users\\<USER>\\Desktop\\MVP\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\MVP\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at C:\\Users\\<USER>\\Desktop\\MVP\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (C:\\Users\\<USER>\\Desktop\\MVP\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Users\\<USER>\\Desktop\\MVP\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\Users\\<USER>\\Desktop\\MVP\\node_modules\\express\\lib\\router\\index.js:175:3)","timestamp":"2025-06-08T14:22:23.976Z"}
{"error":"Code contains potentially malicious patterns","ip":"::1","level":"error","message":"Error occurred:","method":"POST","service":"test-automation-system","stack":"Error: Code contains potentially malicious patterns\n    at Validator.validateCode (C:\\Users\\<USER>\\Desktop\\MVP\\utils\\validator.js:15:13)\n    at generateTests (C:\\Users\\<USER>\\Desktop\\MVP\\controllers\\analysisController.js:47:39)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\MVP\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\Desktop\\MVP\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (C:\\Users\\<USER>\\Desktop\\MVP\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\MVP\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at C:\\Users\\<USER>\\Desktop\\MVP\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (C:\\Users\\<USER>\\Desktop\\MVP\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Users\\<USER>\\Desktop\\MVP\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\Users\\<USER>\\Desktop\\MVP\\node_modules\\express\\lib\\router\\index.js:175:3)","timestamp":"2025-06-08T14:22:23.977Z","url":"/api/generate-tests","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"POST /api/generate-tests - ::1","service":"test-automation-system","timestamp":"2025-06-08T14:22:24.330Z"}
{"level":"error","message":"Test generation error: Code contains potentially malicious patterns","service":"test-automation-system","stack":"Error: Code contains potentially malicious patterns\n    at Validator.validateCode (C:\\Users\\<USER>\\Desktop\\MVP\\utils\\validator.js:15:13)\n    at generateTests (C:\\Users\\<USER>\\Desktop\\MVP\\controllers\\analysisController.js:47:39)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\MVP\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\Desktop\\MVP\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (C:\\Users\\<USER>\\Desktop\\MVP\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\MVP\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at C:\\Users\\<USER>\\Desktop\\MVP\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (C:\\Users\\<USER>\\Desktop\\MVP\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Users\\<USER>\\Desktop\\MVP\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\Users\\<USER>\\Desktop\\MVP\\node_modules\\express\\lib\\router\\index.js:175:3)","timestamp":"2025-06-08T14:22:24.332Z"}
{"error":"Code contains potentially malicious patterns","ip":"::1","level":"error","message":"Error occurred:","method":"POST","service":"test-automation-system","stack":"Error: Code contains potentially malicious patterns\n    at Validator.validateCode (C:\\Users\\<USER>\\Desktop\\MVP\\utils\\validator.js:15:13)\n    at generateTests (C:\\Users\\<USER>\\Desktop\\MVP\\controllers\\analysisController.js:47:39)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\MVP\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\Desktop\\MVP\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (C:\\Users\\<USER>\\Desktop\\MVP\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\MVP\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at C:\\Users\\<USER>\\Desktop\\MVP\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (C:\\Users\\<USER>\\Desktop\\MVP\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Users\\<USER>\\Desktop\\MVP\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\Users\\<USER>\\Desktop\\MVP\\node_modules\\express\\lib\\router\\index.js:175:3)","timestamp":"2025-06-08T14:22:24.333Z","url":"/api/generate-tests","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"POST /api/generate-tests - ::1","service":"test-automation-system","timestamp":"2025-06-08T14:22:24.525Z"}
{"level":"error","message":"Test generation error: Code contains potentially malicious patterns","service":"test-automation-system","stack":"Error: Code contains potentially malicious patterns\n    at Validator.validateCode (C:\\Users\\<USER>\\Desktop\\MVP\\utils\\validator.js:15:13)\n    at generateTests (C:\\Users\\<USER>\\Desktop\\MVP\\controllers\\analysisController.js:47:39)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\MVP\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\Desktop\\MVP\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (C:\\Users\\<USER>\\Desktop\\MVP\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\MVP\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at C:\\Users\\<USER>\\Desktop\\MVP\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (C:\\Users\\<USER>\\Desktop\\MVP\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Users\\<USER>\\Desktop\\MVP\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\Users\\<USER>\\Desktop\\MVP\\node_modules\\express\\lib\\router\\index.js:175:3)","timestamp":"2025-06-08T14:22:24.527Z"}
{"error":"Code contains potentially malicious patterns","ip":"::1","level":"error","message":"Error occurred:","method":"POST","service":"test-automation-system","stack":"Error: Code contains potentially malicious patterns\n    at Validator.validateCode (C:\\Users\\<USER>\\Desktop\\MVP\\utils\\validator.js:15:13)\n    at generateTests (C:\\Users\\<USER>\\Desktop\\MVP\\controllers\\analysisController.js:47:39)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\MVP\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\Desktop\\MVP\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (C:\\Users\\<USER>\\Desktop\\MVP\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\MVP\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at C:\\Users\\<USER>\\Desktop\\MVP\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (C:\\Users\\<USER>\\Desktop\\MVP\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Users\\<USER>\\Desktop\\MVP\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\Users\\<USER>\\Desktop\\MVP\\node_modules\\express\\lib\\router\\index.js:175:3)","timestamp":"2025-06-08T14:22:24.528Z","url":"/api/generate-tests","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"POST /api/generate-tests - ::1","service":"test-automation-system","timestamp":"2025-06-08T14:22:24.691Z"}
{"level":"error","message":"Test generation error: Code contains potentially malicious patterns","service":"test-automation-system","stack":"Error: Code contains potentially malicious patterns\n    at Validator.validateCode (C:\\Users\\<USER>\\Desktop\\MVP\\utils\\validator.js:15:13)\n    at generateTests (C:\\Users\\<USER>\\Desktop\\MVP\\controllers\\analysisController.js:47:39)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\MVP\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\Desktop\\MVP\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (C:\\Users\\<USER>\\Desktop\\MVP\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\MVP\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at C:\\Users\\<USER>\\Desktop\\MVP\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (C:\\Users\\<USER>\\Desktop\\MVP\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Users\\<USER>\\Desktop\\MVP\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\Users\\<USER>\\Desktop\\MVP\\node_modules\\express\\lib\\router\\index.js:175:3)","timestamp":"2025-06-08T14:22:24.693Z"}
{"error":"Code contains potentially malicious patterns","ip":"::1","level":"error","message":"Error occurred:","method":"POST","service":"test-automation-system","stack":"Error: Code contains potentially malicious patterns\n    at Validator.validateCode (C:\\Users\\<USER>\\Desktop\\MVP\\utils\\validator.js:15:13)\n    at generateTests (C:\\Users\\<USER>\\Desktop\\MVP\\controllers\\analysisController.js:47:39)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\MVP\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\Desktop\\MVP\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (C:\\Users\\<USER>\\Desktop\\MVP\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\MVP\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at C:\\Users\\<USER>\\Desktop\\MVP\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (C:\\Users\\<USER>\\Desktop\\MVP\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Users\\<USER>\\Desktop\\MVP\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\Users\\<USER>\\Desktop\\MVP\\node_modules\\express\\lib\\router\\index.js:175:3)","timestamp":"2025-06-08T14:22:24.694Z","url":"/api/generate-tests","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"POST /api/generate-tests - ::1","service":"test-automation-system","timestamp":"2025-06-08T14:22:24.903Z"}
{"level":"error","message":"Test generation error: Code contains potentially malicious patterns","service":"test-automation-system","stack":"Error: Code contains potentially malicious patterns\n    at Validator.validateCode (C:\\Users\\<USER>\\Desktop\\MVP\\utils\\validator.js:15:13)\n    at generateTests (C:\\Users\\<USER>\\Desktop\\MVP\\controllers\\analysisController.js:47:39)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\MVP\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\Desktop\\MVP\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (C:\\Users\\<USER>\\Desktop\\MVP\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\MVP\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at C:\\Users\\<USER>\\Desktop\\MVP\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (C:\\Users\\<USER>\\Desktop\\MVP\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Users\\<USER>\\Desktop\\MVP\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\Users\\<USER>\\Desktop\\MVP\\node_modules\\express\\lib\\router\\index.js:175:3)","timestamp":"2025-06-08T14:22:24.904Z"}
{"error":"Code contains potentially malicious patterns","ip":"::1","level":"error","message":"Error occurred:","method":"POST","service":"test-automation-system","stack":"Error: Code contains potentially malicious patterns\n    at Validator.validateCode (C:\\Users\\<USER>\\Desktop\\MVP\\utils\\validator.js:15:13)\n    at generateTests (C:\\Users\\<USER>\\Desktop\\MVP\\controllers\\analysisController.js:47:39)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\MVP\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\Desktop\\MVP\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (C:\\Users\\<USER>\\Desktop\\MVP\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\MVP\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at C:\\Users\\<USER>\\Desktop\\MVP\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (C:\\Users\\<USER>\\Desktop\\MVP\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Users\\<USER>\\Desktop\\MVP\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\Users\\<USER>\\Desktop\\MVP\\node_modules\\express\\lib\\router\\index.js:175:3)","timestamp":"2025-06-08T14:22:24.905Z","url":"/api/generate-tests","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"GET /demo-formatting.html - ::1","service":"test-automation-system","timestamp":"2025-06-08T14:22:31.832Z"}
{"level":"info","message":"GET /styles.css - ::1","service":"test-automation-system","timestamp":"2025-06-08T14:22:31.920Z"}
{"level":"info","message":"GET / - ::1","service":"test-automation-system","timestamp":"2025-06-08T14:22:39.023Z"}
{"level":"info","message":"GET /styles.css - ::1","service":"test-automation-system","timestamp":"2025-06-08T14:22:39.140Z"}
{"level":"info","message":"GET /script.js - ::1","service":"test-automation-system","timestamp":"2025-06-08T14:22:39.142Z"}
{"level":"info","message":"POST /api/analyze - ::1","service":"test-automation-system","timestamp":"2025-06-08T14:23:00.527Z"}
{"level":"error","message":"Analysis error: Code contains potentially malicious patterns","service":"test-automation-system","stack":"Error: Code contains potentially malicious patterns\n    at Validator.validateCode (C:\\Users\\<USER>\\Desktop\\MVP\\utils\\validator.js:15:13)\n    at analyzeCode (C:\\Users\\<USER>\\Desktop\\MVP\\controllers\\analysisController.js:11:39)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\MVP\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\Desktop\\MVP\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (C:\\Users\\<USER>\\Desktop\\MVP\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\MVP\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at C:\\Users\\<USER>\\Desktop\\MVP\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (C:\\Users\\<USER>\\Desktop\\MVP\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Users\\<USER>\\Desktop\\MVP\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\Users\\<USER>\\Desktop\\MVP\\node_modules\\express\\lib\\router\\index.js:175:3)","timestamp":"2025-06-08T14:23:00.528Z"}
{"error":"Code contains potentially malicious patterns","ip":"::1","level":"error","message":"Error occurred:","method":"POST","service":"test-automation-system","stack":"Error: Code contains potentially malicious patterns\n    at Validator.validateCode (C:\\Users\\<USER>\\Desktop\\MVP\\utils\\validator.js:15:13)\n    at analyzeCode (C:\\Users\\<USER>\\Desktop\\MVP\\controllers\\analysisController.js:11:39)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\MVP\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\Desktop\\MVP\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (C:\\Users\\<USER>\\Desktop\\MVP\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\MVP\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at C:\\Users\\<USER>\\Desktop\\MVP\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (C:\\Users\\<USER>\\Desktop\\MVP\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Users\\<USER>\\Desktop\\MVP\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\Users\\<USER>\\Desktop\\MVP\\node_modules\\express\\lib\\router\\index.js:175:3)","timestamp":"2025-06-08T14:23:00.529Z","url":"/api/analyze","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"GET / - ::1","service":"test-automation-system","timestamp":"2025-06-08T14:23:15.712Z"}
{"level":"info","message":"GET /styles.css - ::1","service":"test-automation-system","timestamp":"2025-06-08T14:23:15.785Z"}
{"level":"info","message":"GET /script.js - ::1","service":"test-automation-system","timestamp":"2025-06-08T14:23:15.789Z"}
{"level":"info","message":"POST /api/generate-tests - ::1","service":"test-automation-system","timestamp":"2025-06-08T14:23:34.157Z"}
{"level":"error","message":"Test generation error: Code contains potentially malicious patterns","service":"test-automation-system","stack":"Error: Code contains potentially malicious patterns\n    at Validator.validateCode (C:\\Users\\<USER>\\Desktop\\MVP\\utils\\validator.js:15:13)\n    at generateTests (C:\\Users\\<USER>\\Desktop\\MVP\\controllers\\analysisController.js:47:39)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\MVP\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\Desktop\\MVP\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (C:\\Users\\<USER>\\Desktop\\MVP\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\MVP\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at C:\\Users\\<USER>\\Desktop\\MVP\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (C:\\Users\\<USER>\\Desktop\\MVP\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Users\\<USER>\\Desktop\\MVP\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\Users\\<USER>\\Desktop\\MVP\\node_modules\\express\\lib\\router\\index.js:175:3)","timestamp":"2025-06-08T14:23:34.159Z"}
{"error":"Code contains potentially malicious patterns","ip":"::1","level":"error","message":"Error occurred:","method":"POST","service":"test-automation-system","stack":"Error: Code contains potentially malicious patterns\n    at Validator.validateCode (C:\\Users\\<USER>\\Desktop\\MVP\\utils\\validator.js:15:13)\n    at generateTests (C:\\Users\\<USER>\\Desktop\\MVP\\controllers\\analysisController.js:47:39)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\MVP\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\Desktop\\MVP\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (C:\\Users\\<USER>\\Desktop\\MVP\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\MVP\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at C:\\Users\\<USER>\\Desktop\\MVP\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (C:\\Users\\<USER>\\Desktop\\MVP\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Users\\<USER>\\Desktop\\MVP\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\Users\\<USER>\\Desktop\\MVP\\node_modules\\express\\lib\\router\\index.js:175:3)","timestamp":"2025-06-08T14:23:34.160Z","url":"/api/generate-tests","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"POST /api/analyze - ::1","service":"test-automation-system","timestamp":"2025-06-08T14:23:35.764Z"}
{"level":"error","message":"Analysis error: Code contains potentially malicious patterns","service":"test-automation-system","stack":"Error: Code contains potentially malicious patterns\n    at Validator.validateCode (C:\\Users\\<USER>\\Desktop\\MVP\\utils\\validator.js:15:13)\n    at analyzeCode (C:\\Users\\<USER>\\Desktop\\MVP\\controllers\\analysisController.js:11:39)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\MVP\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\Desktop\\MVP\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (C:\\Users\\<USER>\\Desktop\\MVP\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\MVP\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at C:\\Users\\<USER>\\Desktop\\MVP\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (C:\\Users\\<USER>\\Desktop\\MVP\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Users\\<USER>\\Desktop\\MVP\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\Users\\<USER>\\Desktop\\MVP\\node_modules\\express\\lib\\router\\index.js:175:3)","timestamp":"2025-06-08T14:23:35.765Z"}
{"error":"Code contains potentially malicious patterns","ip":"::1","level":"error","message":"Error occurred:","method":"POST","service":"test-automation-system","stack":"Error: Code contains potentially malicious patterns\n    at Validator.validateCode (C:\\Users\\<USER>\\Desktop\\MVP\\utils\\validator.js:15:13)\n    at analyzeCode (C:\\Users\\<USER>\\Desktop\\MVP\\controllers\\analysisController.js:11:39)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\MVP\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\Desktop\\MVP\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (C:\\Users\\<USER>\\Desktop\\MVP\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\MVP\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at C:\\Users\\<USER>\\Desktop\\MVP\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (C:\\Users\\<USER>\\Desktop\\MVP\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Users\\<USER>\\Desktop\\MVP\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\Users\\<USER>\\Desktop\\MVP\\node_modules\\express\\lib\\router\\index.js:175:3)","timestamp":"2025-06-08T14:23:35.766Z","url":"/api/analyze","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"POST /api/analyze - ::1","service":"test-automation-system","timestamp":"2025-06-08T14:23:46.317Z"}
{"level":"error","message":"Analysis error: Code contains potentially malicious patterns","service":"test-automation-system","stack":"Error: Code contains potentially malicious patterns\n    at Validator.validateCode (C:\\Users\\<USER>\\Desktop\\MVP\\utils\\validator.js:15:13)\n    at analyzeCode (C:\\Users\\<USER>\\Desktop\\MVP\\controllers\\analysisController.js:11:39)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\MVP\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\Desktop\\MVP\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (C:\\Users\\<USER>\\Desktop\\MVP\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\MVP\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at C:\\Users\\<USER>\\Desktop\\MVP\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (C:\\Users\\<USER>\\Desktop\\MVP\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Users\\<USER>\\Desktop\\MVP\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\Users\\<USER>\\Desktop\\MVP\\node_modules\\express\\lib\\router\\index.js:175:3)","timestamp":"2025-06-08T14:23:46.318Z"}
{"error":"Code contains potentially malicious patterns","ip":"::1","level":"error","message":"Error occurred:","method":"POST","service":"test-automation-system","stack":"Error: Code contains potentially malicious patterns\n    at Validator.validateCode (C:\\Users\\<USER>\\Desktop\\MVP\\utils\\validator.js:15:13)\n    at analyzeCode (C:\\Users\\<USER>\\Desktop\\MVP\\controllers\\analysisController.js:11:39)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\MVP\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\Desktop\\MVP\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (C:\\Users\\<USER>\\Desktop\\MVP\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\MVP\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at C:\\Users\\<USER>\\Desktop\\MVP\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (C:\\Users\\<USER>\\Desktop\\MVP\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Users\\<USER>\\Desktop\\MVP\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\Users\\<USER>\\Desktop\\MVP\\node_modules\\express\\lib\\router\\index.js:175:3)","timestamp":"2025-06-08T14:23:46.319Z","url":"/api/analyze","userAgent":"Mozilla/5.0 (Windows NT; Windows NT 10.0; en-IN) WindowsPowerShell/5.1.26100.4202"}
{"level":"info","message":"POST /api/analyze - ::1","service":"test-automation-system","timestamp":"2025-06-08T14:31:19.310Z"}
{"codeLength":38,"level":"info","message":"Starting code analysis","purposeLength":24,"service":"test-automation-system","timestamp":"2025-06-08T14:31:19.311Z"}
{"level":"info","message":"Code analysis completed successfully","service":"test-automation-system","timestamp":"2025-06-08T14:31:25.613Z"}
{"level":"info","message":"GET / - ::1","service":"test-automation-system","timestamp":"2025-06-08T16:11:53.943Z"}
{"level":"info","message":"GET /styles.css - ::1","service":"test-automation-system","timestamp":"2025-06-08T16:11:54.040Z"}
{"level":"info","message":"GET /script.js - ::1","service":"test-automation-system","timestamp":"2025-06-08T16:11:54.065Z"}
{"level":"info","message":"POST /api/analyze - ::1","service":"test-automation-system","timestamp":"2025-06-08T16:12:08.885Z"}
{"level":"error","message":"Analysis error: Code contains potentially malicious patterns","service":"test-automation-system","stack":"Error: Code contains potentially malicious patterns\n    at Validator.validateCode (C:\\Users\\<USER>\\Desktop\\MVP\\utils\\validator.js:15:13)\n    at analyzeCode (C:\\Users\\<USER>\\Desktop\\MVP\\controllers\\analysisController.js:11:39)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\MVP\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\Desktop\\MVP\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (C:\\Users\\<USER>\\Desktop\\MVP\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\MVP\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at C:\\Users\\<USER>\\Desktop\\MVP\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (C:\\Users\\<USER>\\Desktop\\MVP\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Users\\<USER>\\Desktop\\MVP\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\Users\\<USER>\\Desktop\\MVP\\node_modules\\express\\lib\\router\\index.js:175:3)","timestamp":"2025-06-08T16:12:08.886Z"}
{"error":"Code contains potentially malicious patterns","ip":"::1","level":"error","message":"Error occurred:","method":"POST","service":"test-automation-system","stack":"Error: Code contains potentially malicious patterns\n    at Validator.validateCode (C:\\Users\\<USER>\\Desktop\\MVP\\utils\\validator.js:15:13)\n    at analyzeCode (C:\\Users\\<USER>\\Desktop\\MVP\\controllers\\analysisController.js:11:39)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\MVP\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\Desktop\\MVP\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (C:\\Users\\<USER>\\Desktop\\MVP\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\MVP\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at C:\\Users\\<USER>\\Desktop\\MVP\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (C:\\Users\\<USER>\\Desktop\\MVP\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Users\\<USER>\\Desktop\\MVP\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\Users\\<USER>\\Desktop\\MVP\\node_modules\\express\\lib\\router\\index.js:175:3)","timestamp":"2025-06-08T16:12:08.887Z","url":"/api/analyze","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"GET /styles.css - ::1","service":"test-automation-system","timestamp":"2025-06-08T16:12:15.642Z"}
{"level":"info","message":"GET /.well-known/appspecific/com.chrome.devtools.json - ::1","service":"test-automation-system","timestamp":"2025-06-08T16:12:15.691Z"}
{"level":"info","message":"POST /api/analyze - ::1","service":"test-automation-system","timestamp":"2025-06-08T16:12:22.697Z"}
{"level":"error","message":"Analysis error: Code contains potentially malicious patterns","service":"test-automation-system","stack":"Error: Code contains potentially malicious patterns\n    at Validator.validateCode (C:\\Users\\<USER>\\Desktop\\MVP\\utils\\validator.js:15:13)\n    at analyzeCode (C:\\Users\\<USER>\\Desktop\\MVP\\controllers\\analysisController.js:11:39)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\MVP\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\Desktop\\MVP\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (C:\\Users\\<USER>\\Desktop\\MVP\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\MVP\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at C:\\Users\\<USER>\\Desktop\\MVP\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (C:\\Users\\<USER>\\Desktop\\MVP\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Users\\<USER>\\Desktop\\MVP\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\Users\\<USER>\\Desktop\\MVP\\node_modules\\express\\lib\\router\\index.js:175:3)","timestamp":"2025-06-08T16:12:22.698Z"}
{"error":"Code contains potentially malicious patterns","ip":"::1","level":"error","message":"Error occurred:","method":"POST","service":"test-automation-system","stack":"Error: Code contains potentially malicious patterns\n    at Validator.validateCode (C:\\Users\\<USER>\\Desktop\\MVP\\utils\\validator.js:15:13)\n    at analyzeCode (C:\\Users\\<USER>\\Desktop\\MVP\\controllers\\analysisController.js:11:39)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\MVP\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\Desktop\\MVP\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (C:\\Users\\<USER>\\Desktop\\MVP\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\MVP\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at C:\\Users\\<USER>\\Desktop\\MVP\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (C:\\Users\\<USER>\\Desktop\\MVP\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Users\\<USER>\\Desktop\\MVP\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\Users\\<USER>\\Desktop\\MVP\\node_modules\\express\\lib\\router\\index.js:175:3)","timestamp":"2025-06-08T16:12:22.699Z","url":"/api/analyze","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"POST /api/generate-tests - ::1","service":"test-automation-system","timestamp":"2025-06-08T16:12:34.614Z"}
{"level":"error","message":"Test generation error: Code contains potentially malicious patterns","service":"test-automation-system","stack":"Error: Code contains potentially malicious patterns\n    at Validator.validateCode (C:\\Users\\<USER>\\Desktop\\MVP\\utils\\validator.js:15:13)\n    at generateTests (C:\\Users\\<USER>\\Desktop\\MVP\\controllers\\analysisController.js:47:39)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\MVP\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\Desktop\\MVP\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (C:\\Users\\<USER>\\Desktop\\MVP\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\MVP\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at C:\\Users\\<USER>\\Desktop\\MVP\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (C:\\Users\\<USER>\\Desktop\\MVP\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Users\\<USER>\\Desktop\\MVP\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\Users\\<USER>\\Desktop\\MVP\\node_modules\\express\\lib\\router\\index.js:175:3)","timestamp":"2025-06-08T16:12:34.616Z"}
{"error":"Code contains potentially malicious patterns","ip":"::1","level":"error","message":"Error occurred:","method":"POST","service":"test-automation-system","stack":"Error: Code contains potentially malicious patterns\n    at Validator.validateCode (C:\\Users\\<USER>\\Desktop\\MVP\\utils\\validator.js:15:13)\n    at generateTests (C:\\Users\\<USER>\\Desktop\\MVP\\controllers\\analysisController.js:47:39)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\MVP\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\Desktop\\MVP\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (C:\\Users\\<USER>\\Desktop\\MVP\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\MVP\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at C:\\Users\\<USER>\\Desktop\\MVP\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (C:\\Users\\<USER>\\Desktop\\MVP\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Users\\<USER>\\Desktop\\MVP\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\Users\\<USER>\\Desktop\\MVP\\node_modules\\express\\lib\\router\\index.js:175:3)","timestamp":"2025-06-08T16:12:34.617Z","url":"/api/generate-tests","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"POST /api/generate-tests - ::1","service":"test-automation-system","timestamp":"2025-06-08T16:12:37.230Z"}
{"level":"error","message":"Test generation error: Code contains potentially malicious patterns","service":"test-automation-system","stack":"Error: Code contains potentially malicious patterns\n    at Validator.validateCode (C:\\Users\\<USER>\\Desktop\\MVP\\utils\\validator.js:15:13)\n    at generateTests (C:\\Users\\<USER>\\Desktop\\MVP\\controllers\\analysisController.js:47:39)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\MVP\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\Desktop\\MVP\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (C:\\Users\\<USER>\\Desktop\\MVP\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\MVP\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at C:\\Users\\<USER>\\Desktop\\MVP\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (C:\\Users\\<USER>\\Desktop\\MVP\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Users\\<USER>\\Desktop\\MVP\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\Users\\<USER>\\Desktop\\MVP\\node_modules\\express\\lib\\router\\index.js:175:3)","timestamp":"2025-06-08T16:12:37.231Z"}
{"error":"Code contains potentially malicious patterns","ip":"::1","level":"error","message":"Error occurred:","method":"POST","service":"test-automation-system","stack":"Error: Code contains potentially malicious patterns\n    at Validator.validateCode (C:\\Users\\<USER>\\Desktop\\MVP\\utils\\validator.js:15:13)\n    at generateTests (C:\\Users\\<USER>\\Desktop\\MVP\\controllers\\analysisController.js:47:39)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\MVP\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\Desktop\\MVP\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (C:\\Users\\<USER>\\Desktop\\MVP\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\MVP\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at C:\\Users\\<USER>\\Desktop\\MVP\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (C:\\Users\\<USER>\\Desktop\\MVP\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Users\\<USER>\\Desktop\\MVP\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\Users\\<USER>\\Desktop\\MVP\\node_modules\\express\\lib\\router\\index.js:175:3)","timestamp":"2025-06-08T16:12:37.232Z","url":"/api/generate-tests","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"POST /api/analyze - ::1","service":"test-automation-system","timestamp":"2025-06-08T16:12:38.399Z"}
{"level":"error","message":"Analysis error: Code contains potentially malicious patterns","service":"test-automation-system","stack":"Error: Code contains potentially malicious patterns\n    at Validator.validateCode (C:\\Users\\<USER>\\Desktop\\MVP\\utils\\validator.js:15:13)\n    at analyzeCode (C:\\Users\\<USER>\\Desktop\\MVP\\controllers\\analysisController.js:11:39)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\MVP\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\Desktop\\MVP\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (C:\\Users\\<USER>\\Desktop\\MVP\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\MVP\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at C:\\Users\\<USER>\\Desktop\\MVP\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (C:\\Users\\<USER>\\Desktop\\MVP\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Users\\<USER>\\Desktop\\MVP\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\Users\\<USER>\\Desktop\\MVP\\node_modules\\express\\lib\\router\\index.js:175:3)","timestamp":"2025-06-08T16:12:38.400Z"}
{"error":"Code contains potentially malicious patterns","ip":"::1","level":"error","message":"Error occurred:","method":"POST","service":"test-automation-system","stack":"Error: Code contains potentially malicious patterns\n    at Validator.validateCode (C:\\Users\\<USER>\\Desktop\\MVP\\utils\\validator.js:15:13)\n    at analyzeCode (C:\\Users\\<USER>\\Desktop\\MVP\\controllers\\analysisController.js:11:39)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\MVP\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\Desktop\\MVP\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (C:\\Users\\<USER>\\Desktop\\MVP\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\MVP\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at C:\\Users\\<USER>\\Desktop\\MVP\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (C:\\Users\\<USER>\\Desktop\\MVP\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Users\\<USER>\\Desktop\\MVP\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\Users\\<USER>\\Desktop\\MVP\\node_modules\\express\\lib\\router\\index.js:175:3)","timestamp":"2025-06-08T16:12:38.400Z","url":"/api/analyze","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"POST /api/analyze - ::1","service":"test-automation-system","timestamp":"2025-06-08T16:19:11.544Z"}
{"codeLength":38,"level":"info","message":"Starting code analysis","purposeLength":24,"service":"test-automation-system","timestamp":"2025-06-08T16:19:11.545Z"}
{"level":"info","message":"Code analysis completed successfully","service":"test-automation-system","timestamp":"2025-06-08T16:19:17.612Z"}
{"level":"info","message":"POST /api/analyze - ::1","service":"test-automation-system","timestamp":"2025-06-08T16:19:30.397Z"}
{"level":"info","message":"POST /api/analyze - ::1","service":"test-automation-system","timestamp":"2025-06-08T16:29:13.072Z"}
{"codeLength":38,"level":"info","message":"Starting code analysis","purposeLength":15,"service":"test-automation-system","timestamp":"2025-06-08T16:29:13.073Z"}
{"level":"info","message":"Code analysis completed successfully","service":"test-automation-system","timestamp":"2025-06-08T16:29:19.000Z"}
{"level":"info","message":"POST /api/analyze - ::1","service":"test-automation-system","timestamp":"2025-06-08T16:29:38.109Z"}
{"level":"info","message":"POST /api/analyze - ::1","service":"test-automation-system","timestamp":"2025-06-08T16:30:20.851Z"}
{"codeLength":38,"level":"info","message":"Starting code analysis","purposeLength":15,"service":"test-automation-system","timestamp":"2025-06-08T16:30:20.851Z"}
{"level":"info","message":"Code analysis completed successfully","service":"test-automation-system","timestamp":"2025-06-08T16:30:26.594Z"}
{"level":"info","message":"POST /api/analyze - ::1","service":"test-automation-system","timestamp":"2025-06-08T16:30:47.129Z"}
{"level":"info","message":"GET / - ::1","service":"test-automation-system","timestamp":"2025-06-08T16:31:14.303Z"}
{"level":"info","message":"GET /styles.css - ::1","service":"test-automation-system","timestamp":"2025-06-08T16:31:14.454Z"}
{"level":"info","message":"GET /script.js - ::1","service":"test-automation-system","timestamp":"2025-06-08T16:31:14.456Z"}
{"level":"info","message":"POST /api/analyze - ::1","service":"test-automation-system","timestamp":"2025-06-08T16:32:21.029Z"}
{"level":"info","message":"POST /api/analyze - ::1","service":"test-automation-system","timestamp":"2025-06-08T16:36:26.412Z"}
{"level":"info","message":"Test Automation System running on port 3001","service":"test-automation-system","timestamp":"2025-06-08T16:43:12.489Z"}
{"level":"info","message":"Environment: development","service":"test-automation-system","timestamp":"2025-06-08T16:43:12.493Z"}
{"level":"info","message":"Test Automation System running on port 3001","service":"test-automation-system","timestamp":"2025-06-09T06:46:40.389Z"}
{"level":"info","message":"Environment: development","service":"test-automation-system","timestamp":"2025-06-09T06:46:40.398Z"}
{"level":"info","message":"GET / - ::1","service":"test-automation-system","timestamp":"2025-06-09T06:46:48.988Z"}
{"level":"info","message":"GET /styles.css - ::1","service":"test-automation-system","timestamp":"2025-06-09T06:46:49.106Z"}
{"level":"info","message":"GET /script.js - ::1","service":"test-automation-system","timestamp":"2025-06-09T06:46:49.109Z"}
{"level":"info","message":"GET /favicon.ico - ::1","service":"test-automation-system","timestamp":"2025-06-09T06:46:49.238Z"}
{"level":"info","message":"Test Automation System running on port 3001","service":"test-automation-system","timestamp":"2025-06-09T06:47:15.902Z"}
{"level":"info","message":"Environment: development","service":"test-automation-system","timestamp":"2025-06-09T06:47:15.906Z"}
{"level":"info","message":"Test Automation System running on port 3001","service":"test-automation-system","timestamp":"2025-06-09T06:53:05.799Z"}
{"level":"info","message":"Environment: development","service":"test-automation-system","timestamp":"2025-06-09T06:53:05.804Z"}
{"level":"info","message":"Test Automation System running on port 3001","service":"test-automation-system","timestamp":"2025-06-09T06:53:19.343Z"}
{"level":"info","message":"Environment: development","service":"test-automation-system","timestamp":"2025-06-09T06:53:19.347Z"}
{"level":"info","message":"Test Automation System running on port 3001","service":"test-automation-system","timestamp":"2025-06-09T06:53:24.950Z"}
{"level":"info","message":"Environment: development","service":"test-automation-system","timestamp":"2025-06-09T06:53:24.959Z"}
{"level":"info","message":"Test Automation System running on port 3001","service":"test-automation-system","timestamp":"2025-06-09T06:53:40.811Z"}
{"level":"info","message":"Environment: development","service":"test-automation-system","timestamp":"2025-06-09T06:53:40.818Z"}
