{"name": "test-automation-system", "version": "1.0.0", "description": "A stateless RESTful backend for test automation with DeepSeek API integration", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["test-automation", "express", "deepseek", "code-analysis"], "author": "", "license": "MIT", "dependencies": {"adm-zip": "^0.5.10", "axios": "^1.6.0", "bcryptjs": "^2.4.3", "cors": "^2.8.5", "dotenv": "^16.4.5", "express": "^4.18.2", "express-rate-limit": "^7.1.5", "helmet": "^7.1.0", "jsonwebtoken": "^9.0.0", "mongoose": "^7.0.0", "multer": "^1.4.5-lts.1", "openai": "^4.28.4", "sanitize-html": "^2.11.0", "winston": "^3.11.0"}, "devDependencies": {"nodemon": "^3.0.2"}, "engines": {"node": ">=18.0.0"}}