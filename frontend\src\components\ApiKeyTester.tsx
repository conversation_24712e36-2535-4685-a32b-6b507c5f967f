import { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { 
  Key, 
  Search, 
  CheckCircle, 
  XCircle, 
  Loader2,
  Brain,
  Zap,
  Globe,
  Bot,
  Sparkles
} from 'lucide-react';
import { toast } from 'sonner';

const ApiKeyTester = () => {
  const [apiKey, setApiKey] = useState('');
  const [apiProvider, setApiProvider] = useState('deepseek');
  const [isTestingApiKey, setIsTestingApiKey] = useState(false);
  const [apiKeyStatus, setApiKeyStatus] = useState('');
  const [testResult, setTestResult] = useState(null);

  const apiProviders = [
    { value: 'openai', label: 'OpenAI', icon: Brain, color: 'text-green-600' },
    { value: 'deepseek', label: 'DeepSeek', icon: Zap, color: 'text-blue-600' },
    { value: 'huggingface', label: 'Hugging Face', icon: Bot, color: 'text-yellow-600' },
    { value: 'anthropic', label: 'Anthropic (Claude)', icon: Sparkles, color: 'text-purple-600' },
    { value: 'google', label: 'Google AI', icon: Globe, color: 'text-red-600' },
  ];

  const testApiKey = async () => {
    if (!apiKey.trim()) {
      toast.error('Please enter an API key');
      return;
    }

    setIsTestingApiKey(true);
    setTestResult(null);
    
    try {
      const response = await fetch('/api/test-api-key', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ 
          apiKey: apiKey.trim(),
          apiProvider: apiProvider 
        }),
      });

      const result = await response.json();
      
      if (result.status === 'success') {
        setApiKeyStatus('valid');
        setTestResult(result.data);
        toast.success(`✅ ${result.message}`);
      } else {
        setApiKeyStatus('invalid');
        setTestResult(result.data);
        toast.error(`❌ ${result.error}`);
      }
    } catch (error) {
      setApiKeyStatus('invalid');
      toast.error('❌ Failed to test API key');
    } finally {
      setIsTestingApiKey(false);
    }
  };

  const selectedProvider = apiProviders.find(p => p.value === apiProvider);

  return (
    <Card className="w-full max-w-2xl mx-auto shadow-xl border-0 bg-white/50 dark:bg-slate-800/50 backdrop-blur-lg">
      <CardHeader>
        <CardTitle className="flex items-center gap-2 text-2xl">
          <Key className="h-6 w-6 text-indigo-600" />
          API Key Tester
        </CardTitle>
        <CardDescription>
          Test your AI API keys to ensure they're working correctly
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* API Provider Selection */}
        <div className="space-y-2">
          <label className="text-sm font-medium">Select AI Provider</label>
          <Select value={apiProvider} onValueChange={setApiProvider}>
            <SelectTrigger className="w-full">
              <SelectValue placeholder="Choose an AI provider" />
            </SelectTrigger>
            <SelectContent>
              {apiProviders.map((provider) => (
                <SelectItem key={provider.value} value={provider.value}>
                  <div className="flex items-center gap-2">
                    <provider.icon className={`h-4 w-4 ${provider.color}`} />
                    <span>{provider.label}</span>
                  </div>
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        {/* API Key Input */}
        <div className="space-y-2">
          <label className="text-sm font-medium">
            {selectedProvider?.label} API Key
          </label>
          <div className="flex gap-3">
            <div className="relative flex-1">
              <Key className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
              <Input
                type="password"
                placeholder={`Enter your ${selectedProvider?.label} API key`}
                value={apiKey}
                onChange={(e) => setApiKey(e.target.value)}
                className="pl-10"
              />
            </div>
            <Button
              onClick={testApiKey}
              disabled={isTestingApiKey || !apiKey.trim()}
              className="flex items-center gap-2 bg-gradient-to-r from-indigo-600 to-purple-600 hover:from-indigo-700 hover:to-purple-700 text-white border-0"
            >
              {isTestingApiKey ? (
                <Loader2 className="h-4 w-4 animate-spin" />
              ) : (
                <Search className="h-4 w-4" />
              )}
              Test API Key
            </Button>
          </div>
        </div>

        {/* API Key Status */}
        {apiKeyStatus && (
          <div className={`flex items-center gap-2 p-4 rounded-lg border ${
            apiKeyStatus === 'valid' 
              ? 'bg-green-50 dark:bg-green-900/20 border-green-200 dark:border-green-800 text-green-700 dark:text-green-300' 
              : 'bg-red-50 dark:bg-red-900/20 border-red-200 dark:border-red-800 text-red-700 dark:text-red-300'
          }`}>
            {apiKeyStatus === 'valid' ? (
              <CheckCircle className="h-5 w-5" />
            ) : (
              <XCircle className="h-5 w-5" />
            )}
            <div className="flex-1">
              <p className="font-medium">
                {apiKeyStatus === 'valid' ? 'API Key is Valid' : 'API Key is Invalid'}
              </p>
              {testResult && (
                <div className="text-sm mt-1 space-y-1">
                  <p>Provider: {testResult.provider}</p>
                  {testResult.model && <p>Model: {testResult.model}</p>}
                  <p>Tested: {new Date(testResult.timestamp).toLocaleString()}</p>
                </div>
              )}
            </div>
          </div>
        )}

        {/* Provider Information */}
        <div className="bg-gray-50 dark:bg-slate-700/50 rounded-lg p-4">
          <h4 className="font-medium mb-2 flex items-center gap-2">
            {selectedProvider && <selectedProvider.icon className={`h-4 w-4 ${selectedProvider.color}`} />}
            {selectedProvider?.label} Information
          </h4>
          <div className="text-sm text-muted-foreground space-y-1">
            {apiProvider === 'openai' && (
              <>
                <p>• Get your API key from: <a href="https://platform.openai.com/api-keys" target="_blank" rel="noopener noreferrer" className="text-blue-600 hover:underline">OpenAI Platform</a></p>
                <p>• Supports: GPT-3.5, GPT-4, and other OpenAI models</p>
              </>
            )}
            {apiProvider === 'deepseek' && (
              <>
                <p>• Get your API key from: <a href="https://platform.deepseek.com" target="_blank" rel="noopener noreferrer" className="text-blue-600 hover:underline">DeepSeek Platform</a></p>
                <p>• Supports: DeepSeek Coder and Chat models</p>
              </>
            )}
            {apiProvider === 'huggingface' && (
              <>
                <p>• Get your API key from: <a href="https://huggingface.co/settings/tokens" target="_blank" rel="noopener noreferrer" className="text-blue-600 hover:underline">Hugging Face Tokens</a></p>
                <p>• Supports: Thousands of open-source models</p>
              </>
            )}
            {apiProvider === 'anthropic' && (
              <>
                <p>• Get your API key from: <a href="https://console.anthropic.com" target="_blank" rel="noopener noreferrer" className="text-blue-600 hover:underline">Anthropic Console</a></p>
                <p>• Supports: Claude 3 models (Haiku, Sonnet, Opus)</p>
              </>
            )}
            {apiProvider === 'google' && (
              <>
                <p>• Get your API key from: <a href="https://makersuite.google.com/app/apikey" target="_blank" rel="noopener noreferrer" className="text-blue-600 hover:underline">Google AI Studio</a></p>
                <p>• Supports: Gemini Pro and other Google AI models</p>
              </>
            )}
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default ApiKeyTester;
