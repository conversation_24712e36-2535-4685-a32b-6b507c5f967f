const Validator = require('../utils/validator');
const deepseekService = require('../services/deepseekService');
const logger = require('../utils/logger');

class AnalysisController {
  async analyzeCode(req, res, next) {
    try {
      const { code, purpose } = req.body;

      // Validate inputs
      const sanitizedCode = Validator.validateCode(code);
      const sanitizedPurpose = Validator.validatePurpose(purpose);

      // Note: Removed JavaScript-only syntax validation to support any programming language

      logger.info('Starting code analysis', {
        codeLength: sanitizedCode.length,
        purposeLength: sanitizedPurpose.length
      });

      // Perform analysis using DeepSeek API
      const analysisResult = await deepseekService.analyzeCode(sanitizedCode, sanitizedPurpose);

      res.json({
        status: 'success',
        data: analysisResult,
        timestamp: new Date().toISOString()
      });

    } catch (error) {
      logger.error('Analysis error:', error);
      next(error);
    }
  }

  async generateTests(req, res, next) {
    try {
      const { code, purpose } = req.body;

      // Validate inputs
      const sanitizedCode = Validator.validateCode(code);
      const sanitizedPurpose = Validator.validatePurpose(purpose);

      // Note: Removed JavaScript-only syntax validation to support any programming language

      logger.info('Starting test generation', {
        codeLength: sanitizedCode.length,
        purposeLength: sanitizedPurpose.length
      });

      // Generate tests using DeepSeek API
      const testResult = await deepseekService.generateTests(sanitizedCode, sanitizedPurpose);

      res.json({
        status: 'success',
        data: testResult,
        timestamp: new Date().toISOString()
      });

    } catch (error) {
      logger.error('Test generation error:', error);
      next(error);
    }
  }
}

module.exports = new AnalysisController();
