<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI QA Assistant - Powered by DeepSeek</title>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="styles.css">
</head>
<body class="dark-theme">
    <!-- Auth Modal -->
    <div id="authModal" class="modal-overlay">
        <div class="auth-container">
            <div class="auth-header">
                <div class="logo">
                    <i class="fas fa-brain"></i>
                    <span>AI QA Assistant</span>
                </div>
                <p class="tagline">Powered by DeepSeek AI</p>
            </div>

            <div class="auth-tabs">
                <button class="auth-tab active" data-tab="login">Sign In</button>
                <button class="auth-tab" data-tab="signup">Sign Up</button>
            </div>

            <form id="loginForm" class="auth-form active">
                <div class="input-group">
                    <i class="fas fa-envelope"></i>
                    <input type="email" placeholder="Email address" required>
                </div>
                <div class="input-group">
                    <i class="fas fa-lock"></i>
                    <input type="password" placeholder="Password" required>
                </div>
                <button type="submit" class="auth-btn">
                    <span>Sign In</span>
                    <i class="fas fa-arrow-right"></i>
                </button>
            </form>

            <form id="signupForm" class="auth-form">
                <div class="input-group">
                    <i class="fas fa-user"></i>
                    <input type="text" placeholder="Full name" required>
                </div>
                <div class="input-group">
                    <i class="fas fa-envelope"></i>
                    <input type="email" placeholder="Email address" required>
                </div>
                <div class="input-group">
                    <i class="fas fa-lock"></i>
                    <input type="password" placeholder="Password" required>
                </div>
                <button type="submit" class="auth-btn">
                    <span>Create Account</span>
                    <i class="fas fa-arrow-right"></i>
                </button>
            </form>
        </div>
    </div>

    <!-- Role Selector -->
    <div id="roleSelector" class="role-selector hidden">
        <div class="role-header">
            <h1>Choose Your Role</h1>
            <p>Select your primary role to get customized AI analysis</p>
        </div>

        <div class="role-cards">
            <div class="role-card" data-role="developer">
                <div class="role-icon">👨‍💻</div>
                <h3>Developer</h3>
                <p>Get code corrections, bug analysis, and optimization suggestions</p>
                <div class="role-features">
                    <span>• Code Review</span>
                    <span>• Bug Detection</span>
                    <span>• Performance Tips</span>
                </div>
            </div>

            <div class="role-card" data-role="tester">
                <div class="role-icon">🧪</div>
                <h3>Tester</h3>
                <p>Generate comprehensive test cases and security assessments</p>
                <div class="role-features">
                    <span>• Test Generation</span>
                    <span>• Security Tests</span>
                    <span>• Edge Cases</span>
                </div>
            </div>

            <div class="role-card" data-role="product-owner">
                <div class="role-icon">📊</div>
                <h3>Product Owner</h3>
                <p>Get business logic validation and feature analysis reports</p>
                <div class="role-features">
                    <span>• Feature Validation</span>
                    <span>• Bug Analytics</span>
                    <span>• Reports</span>
                </div>
            </div>
        </div>
    </div>

    <!-- Main App -->
    <div id="mainApp" class="main-app hidden">
        <!-- Header -->
        <header class="app-header">
            <div class="header-left">
                <div class="logo">
                    <i class="fas fa-brain"></i>
                    <span>AI QA Assistant</span>
                </div>
            </div>

            <div class="header-center">
                <div class="language-selector">
                    <select id="languageSelect">
                        <option value="javascript">JavaScript</option>
                        <option value="python">Python</option>
                        <option value="java">Java</option>
                        <option value="csharp">C#</option>
                        <option value="cpp">C++</option>
                        <option value="php">PHP</option>
                        <option value="ruby">Ruby</option>
                        <option value="go">Go</option>
                    </select>
                </div>
            </div>

            <div class="header-right">
                <button id="themeToggle" class="theme-toggle">
                    <i class="fas fa-moon"></i>
                </button>
                <div class="user-menu">
                    <div class="user-avatar" id="userRole">
                        <span class="role-emoji">👨‍💻</span>
                    </div>
                    <div class="user-dropdown">
                        <button id="logoutBtn">Logout</button>
                    </div>
                </div>
            </div>
        </header>

        <!-- Input Section -->
        <div class="input-section">
            <div class="input-method-panel">
                <div class="method-tabs">
                    <button class="method-tab active" data-method="paste">
                        <i class="fas fa-code"></i>
                        <span>Paste Code</span>
                    </button>
                    <button class="method-tab" data-method="github">
                        <i class="fab fa-github"></i>
                        <span>GitHub URL</span>
                    </button>
                    <button class="method-tab" data-method="upload">
                        <i class="fas fa-upload"></i>
                        <span>Upload .zip</span>
                    </button>
                </div>

                <div class="method-content">
                    <div id="pasteMethod" class="method-panel active">
                        <div class="code-editor">
                            <textarea id="codeInput" placeholder="Paste your code here..."></textarea>
                        </div>
                    </div>

                    <div id="githubMethod" class="method-panel">
                        <div class="github-input">
                            <i class="fab fa-github"></i>
                            <input type="url" id="githubUrl" placeholder="https://github.com/username/repository">
                            <button id="fetchGithub">Fetch</button>
                        </div>
                    </div>

                    <div id="uploadMethod" class="method-panel">
                        <div class="upload-zone" id="uploadZone">
                            <i class="fas fa-cloud-upload-alt"></i>
                            <p>Drop your .zip file here or click to browse</p>
                            <input type="file" id="fileInput" accept=".zip" hidden>
                        </div>
                    </div>
                </div>
            </div>

            <div class="prompt-section">
                <div class="prompt-box">
                    <label for="promptInput">Describe what your code is supposed to do</label>
                    <textarea id="promptInput" placeholder="Enter a clear description of your code's purpose and expected behavior..."></textarea>
                </div>
            </div>

            <div class="api-test-section">
                <div class="api-test-box">
                    <div class="api-input">
                        <i class="fas fa-key"></i>
                        <input type="password" id="apiKeyInput" placeholder="Enter DeepSeek API Key to Test">
                        <button id="testApiKey">
                            <i class="fas fa-search"></i>
                            Test API Key
                        </button>
                    </div>
                    <div id="apiStatus" class="api-status"></div>
                </div>
            </div>

            <div class="submit-section">
                <button id="analyzeBtn" class="analyze-btn" disabled>
                    <i class="fas fa-brain"></i>
                    <span>Analyze with DeepSeek AI</span>
                    <div class="btn-glow"></div>
                </button>
            </div>
        </div>

        <!-- Output Section -->
        <div id="outputSection" class="output-section hidden">
            <div class="output-header">
                <h2>Analysis Results</h2>
                <div class="output-actions">
                    <button class="export-btn" data-format="json">
                        <i class="fas fa-download"></i>
                        Export JSON
                    </button>
                    <button class="export-btn" data-format="pdf">
                        <i class="fas fa-file-pdf"></i>
                        Export PDF
                    </button>
                </div>
            </div>

            <!-- Developer Output -->
            <div id="developerOutput" class="role-output">
                <div class="output-grid">
                    <div class="output-card">
                        <div class="card-header">
                            <i class="fas fa-tools"></i>
                            <h3>Corrected Code</h3>
                            <button class="copy-btn" data-copy="corrected-code">
                                <i class="fas fa-copy"></i>
                            </button>
                        </div>
                        <div class="card-content">
                            <div id="correctedCode" class="code-block"></div>
                        </div>
                    </div>

                    <div class="output-card">
                        <div class="card-header">
                            <i class="fas fa-bug"></i>
                            <h3>Bug Analysis</h3>
                        </div>
                        <div class="card-content">
                            <div id="bugAnalysis" class="analysis-content"></div>
                        </div>
                    </div>

                    <div class="output-card">
                        <div class="card-header">
                            <i class="fas fa-chart-line"></i>
                            <h3>Performance Insights</h3>
                        </div>
                        <div class="card-content">
                            <div id="performanceInsights" class="analysis-content"></div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Tester Output -->
            <div id="testerOutput" class="role-output">
                <div class="test-categories">
                    <div class="test-category">
                        <div class="category-header" data-toggle="functional-tests">
                            <i class="fas fa-check-circle"></i>
                            <h3>Functional Test Cases</h3>
                            <span class="test-count" id="functionalCount">0</span>
                            <i class="fas fa-chevron-down toggle-icon"></i>
                        </div>
                        <div id="functionalTests" class="category-content"></div>
                    </div>

                    <div class="test-category">
                        <div class="category-header" data-toggle="security-tests">
                            <i class="fas fa-shield-alt"></i>
                            <h3>Security Test Cases</h3>
                            <span class="test-count" id="securityCount">0</span>
                            <i class="fas fa-chevron-down toggle-icon"></i>
                        </div>
                        <div id="securityTests" class="category-content"></div>
                    </div>

                    <div class="test-category">
                        <div class="category-header" data-toggle="performance-tests">
                            <i class="fas fa-tachometer-alt"></i>
                            <h3>Performance Test Cases</h3>
                            <span class="test-count" id="performanceCount">0</span>
                            <i class="fas fa-chevron-down toggle-icon"></i>
                        </div>
                        <div id="performanceTests" class="category-content"></div>
                    </div>
                </div>
            </div>

            <!-- Product Owner Output -->
            <div id="productOwnerOutput" class="role-output">
                <div class="dashboard-grid">
                    <div class="dashboard-card">
                        <div class="card-header">
                            <i class="fas fa-chart-pie"></i>
                            <h3>Bug Statistics</h3>
                        </div>
                        <div class="card-content">
                            <canvas id="bugStatsChart"></canvas>
                        </div>
                    </div>

                    <div class="dashboard-card">
                        <div class="card-header">
                            <i class="fas fa-tasks"></i>
                            <h3>Feature Validation</h3>
                        </div>
                        <div class="card-content">
                            <div id="featureValidation" class="validation-content"></div>
                        </div>
                    </div>

                    <div class="dashboard-card">
                        <div class="card-header">
                            <i class="fas fa-timeline"></i>
                            <h3>Analysis Summary</h3>
                        </div>
                        <div class="card-content">
                            <div id="analysisSummary" class="summary-content"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Loading Overlay -->
        <div id="loadingOverlay" class="loading-overlay hidden">
            <div class="loading-content">
                <div class="ai-brain">
                    <i class="fas fa-brain"></i>
                </div>
                <div class="loading-text">
                    <h3>Analyzing with DeepSeek AI...</h3>
                    <p id="loadingStatus">Initializing analysis...</p>
                </div>
                <div class="loading-progress">
                    <div class="progress-bar">
                        <div class="progress-fill"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Toast Notifications -->
    <div id="toastContainer" class="toast-container"></div>

    <script src="https://cdnjs.cloudflare.com/ajax/libs/Chart.js/3.9.1/chart.min.js"></script>
    <script src="script.js"></script>
</body>
</html>