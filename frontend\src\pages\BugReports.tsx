import { useState, useEffect } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Textarea } from '@/components/ui/textarea';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { 
  Bug, 
  Plus, 
  AlertTriangle, 
  CheckCircle, 
  Clock, 
  Filter,
  Search,
  FileText,
  Code,
  Brain,
  Target,
  Shield
} from 'lucide-react';
import { toast } from 'sonner';

const BugReports = () => {
  const { user } = useAuth();
  const [bugReports, setBugReports] = useState([]);
  const [isLoading, setIsLoading] = useState(false);
  const [isCreating, setIsCreating] = useState(false);
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  
  // Bug form state
  const [title, setTitle] = useState('');
  const [description, setDescription] = useState('');
  const [severity, setSeverity] = useState('medium');
  const [priority, setPriority] = useState('medium');
  const [stepsToReproduce, setStepsToReproduce] = useState(['']);
  const [expectedBehavior, setExpectedBehavior] = useState('');
  const [actualBehavior, setActualBehavior] = useState('');
  const [environment, setEnvironment] = useState('');
  const [code, setCode] = useState('');
  const [language, setLanguage] = useState('javascript');

  // Filter state
  const [statusFilter, setStatusFilter] = useState('all');
  const [severityFilter, setSeverityFilter] = useState('all');

  const severityColors = {
    low: 'bg-green-100 text-green-800',
    medium: 'bg-yellow-100 text-yellow-800',
    high: 'bg-orange-100 text-orange-800',
    critical: 'bg-red-100 text-red-800'
  };

  const statusColors = {
    open: 'bg-blue-100 text-blue-800',
    'in-progress': 'bg-purple-100 text-purple-800',
    resolved: 'bg-green-100 text-green-800',
    closed: 'bg-gray-100 text-gray-800'
  };

  const languages = [
    'javascript', 'python', 'java', 'typescript', 'csharp', 'cpp', 'php', 'ruby', 'go', 'rust'
  ];

  useEffect(() => {
    fetchBugReports();
  }, []);

  const fetchBugReports = async () => {
    setIsLoading(true);
    try {
      const response = await fetch('/api/bug-reports', {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });
      
      const data = await response.json();
      if (data.status === 'success') {
        setBugReports(data.data.bugReports);
      }
    } catch (error) {
      toast.error('Failed to fetch bug reports');
    } finally {
      setIsLoading(false);
    }
  };

  const createBugReport = async () => {
    if (!title.trim() || !description.trim()) {
      toast.error('Title and description are required');
      return;
    }

    setIsCreating(true);
    try {
      const response = await fetch('/api/create-bug-report', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        },
        body: JSON.stringify({
          title,
          description,
          severity,
          priority,
          stepsToReproduce: stepsToReproduce.filter(step => step.trim()),
          expectedBehavior,
          actualBehavior,
          environment: environment ? JSON.parse(environment) : {},
          code,
          language
        })
      });

      const data = await response.json();
      
      if (data.status === 'success') {
        toast.success('Bug report created successfully!');
        resetForm();
        setIsDialogOpen(false);
        fetchBugReports();
      } else {
        toast.error(data.error || 'Failed to create bug report');
      }
    } catch (error) {
      toast.error('Network error occurred');
    } finally {
      setIsCreating(false);
    }
  };

  const resetForm = () => {
    setTitle('');
    setDescription('');
    setSeverity('medium');
    setPriority('medium');
    setStepsToReproduce(['']);
    setExpectedBehavior('');
    setActualBehavior('');
    setEnvironment('');
    setCode('');
    setLanguage('javascript');
  };

  const addStep = () => {
    setStepsToReproduce([...stepsToReproduce, '']);
  };

  const updateStep = (index: number, value: string) => {
    const newSteps = [...stepsToReproduce];
    newSteps[index] = value;
    setStepsToReproduce(newSteps);
  };

  const removeStep = (index: number) => {
    if (stepsToReproduce.length > 1) {
      setStepsToReproduce(stepsToReproduce.filter((_, i) => i !== index));
    }
  };

  const filteredBugReports = bugReports.filter(bug => {
    const statusMatch = statusFilter === 'all' || bug.status === statusFilter;
    const severityMatch = severityFilter === 'all' || bug.severity === severityFilter;
    return statusMatch && severityMatch;
  });

  const renderAIAnalysis = (aiAnalysis: any) => {
    if (!aiAnalysis) return null;

    return (
      <Card className="mt-4">
        <CardHeader>
          <CardTitle className="flex items-center gap-2 text-sm">
            <Brain className="h-4 w-4" />
            AI Analysis
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-3">
          {aiAnalysis.rootCause && (
            <div>
              <h4 className="font-medium text-sm mb-1">Root Cause</h4>
              <p className="text-sm text-muted-foreground">{aiAnalysis.rootCause}</p>
            </div>
          )}
          {aiAnalysis.potentialFixes && (
            <div>
              <h4 className="font-medium text-sm mb-1">Potential Fixes</h4>
              <ul className="text-sm text-muted-foreground space-y-1">
                {Array.isArray(aiAnalysis.potentialFixes) 
                  ? aiAnalysis.potentialFixes.map((fix, index) => (
                      <li key={index}>• {fix}</li>
                    ))
                  : <li>• {aiAnalysis.potentialFixes}</li>
                }
              </ul>
            </div>
          )}
          {aiAnalysis.impactAssessment && (
            <div>
              <h4 className="font-medium text-sm mb-1">Impact Assessment</h4>
              <p className="text-sm text-muted-foreground">{aiAnalysis.impactAssessment}</p>
            </div>
          )}
        </CardContent>
      </Card>
    );
  };

  return (
    <div className="container mx-auto px-4 py-8 max-w-6xl">
      <div className="flex items-center justify-between mb-8">
        <div>
          <h1 className="text-4xl font-bold mb-2">Bug Reports</h1>
          <p className="text-muted-foreground">
            Track and manage bugs with AI-powered analysis
          </p>
        </div>
        
        <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
          <DialogTrigger asChild>
            <Button size="lg">
              <Plus className="mr-2 h-4 w-4" />
              Create Bug Report
            </Button>
          </DialogTrigger>
          <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
            <DialogHeader>
              <DialogTitle>Create New Bug Report</DialogTitle>
              <DialogDescription>
                Provide detailed information about the bug for AI-powered analysis
              </DialogDescription>
            </DialogHeader>
            
            <div className="space-y-4">
              <div>
                <label className="text-sm font-medium mb-2 block">Title *</label>
                <Input
                  placeholder="Brief description of the bug"
                  value={title}
                  onChange={(e) => setTitle(e.target.value)}
                />
              </div>

              <div>
                <label className="text-sm font-medium mb-2 block">Description *</label>
                <Textarea
                  placeholder="Detailed description of the bug"
                  value={description}
                  onChange={(e) => setDescription(e.target.value)}
                  rows={4}
                />
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="text-sm font-medium mb-2 block">Severity</label>
                  <Select value={severity} onValueChange={setSeverity}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="low">Low</SelectItem>
                      <SelectItem value="medium">Medium</SelectItem>
                      <SelectItem value="high">High</SelectItem>
                      <SelectItem value="critical">Critical</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div>
                  <label className="text-sm font-medium mb-2 block">Priority</label>
                  <Select value={priority} onValueChange={setPriority}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="low">Low</SelectItem>
                      <SelectItem value="medium">Medium</SelectItem>
                      <SelectItem value="high">High</SelectItem>
                      <SelectItem value="urgent">Urgent</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div>
                <label className="text-sm font-medium mb-2 block">Steps to Reproduce</label>
                {stepsToReproduce.map((step, index) => (
                  <div key={index} className="flex gap-2 mb-2">
                    <Input
                      placeholder={`Step ${index + 1}`}
                      value={step}
                      onChange={(e) => updateStep(index, e.target.value)}
                    />
                    {stepsToReproduce.length > 1 && (
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => removeStep(index)}
                      >
                        Remove
                      </Button>
                    )}
                  </div>
                ))}
                <Button variant="outline" size="sm" onClick={addStep}>
                  Add Step
                </Button>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="text-sm font-medium mb-2 block">Expected Behavior</label>
                  <Textarea
                    placeholder="What should happen"
                    value={expectedBehavior}
                    onChange={(e) => setExpectedBehavior(e.target.value)}
                    rows={3}
                  />
                </div>

                <div>
                  <label className="text-sm font-medium mb-2 block">Actual Behavior</label>
                  <Textarea
                    placeholder="What actually happens"
                    value={actualBehavior}
                    onChange={(e) => setActualBehavior(e.target.value)}
                    rows={3}
                  />
                </div>
              </div>

              <div>
                <label className="text-sm font-medium mb-2 block">Related Code (Optional)</label>
                <div className="grid grid-cols-3 gap-2 mb-2">
                  <Select value={language} onValueChange={setLanguage}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      {languages.map(lang => (
                        <SelectItem key={lang} value={lang}>
                          {lang.charAt(0).toUpperCase() + lang.slice(1)}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
                <Textarea
                  placeholder="Paste relevant code here for AI analysis"
                  value={code}
                  onChange={(e) => setCode(e.target.value)}
                  rows={6}
                  className="font-mono text-sm"
                />
              </div>

              <div className="flex gap-2 pt-4">
                <Button onClick={createBugReport} disabled={isCreating} className="flex-1">
                  {isCreating ? (
                    <>
                      <Clock className="mr-2 h-4 w-4 animate-spin" />
                      Creating...
                    </>
                  ) : (
                    <>
                      <Bug className="mr-2 h-4 w-4" />
                      Create Bug Report
                    </>
                  )}
                </Button>
                <Button variant="outline" onClick={() => setIsDialogOpen(false)}>
                  Cancel
                </Button>
              </div>
            </div>
          </DialogContent>
        </Dialog>
      </div>

      {/* Filters */}
      <div className="flex gap-4 mb-6">
        <Select value={statusFilter} onValueChange={setStatusFilter}>
          <SelectTrigger className="w-40">
            <SelectValue placeholder="Filter by status" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Status</SelectItem>
            <SelectItem value="open">Open</SelectItem>
            <SelectItem value="in-progress">In Progress</SelectItem>
            <SelectItem value="resolved">Resolved</SelectItem>
            <SelectItem value="closed">Closed</SelectItem>
          </SelectContent>
        </Select>

        <Select value={severityFilter} onValueChange={setSeverityFilter}>
          <SelectTrigger className="w-40">
            <SelectValue placeholder="Filter by severity" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Severity</SelectItem>
            <SelectItem value="low">Low</SelectItem>
            <SelectItem value="medium">Medium</SelectItem>
            <SelectItem value="high">High</SelectItem>
            <SelectItem value="critical">Critical</SelectItem>
          </SelectContent>
        </Select>
      </div>

      {/* Bug Reports List */}
      <div className="space-y-4">
        {isLoading ? (
          <div className="text-center py-8">
            <Clock className="h-8 w-8 animate-spin mx-auto mb-4" />
            <p>Loading bug reports...</p>
          </div>
        ) : filteredBugReports.length === 0 ? (
          <Card>
            <CardContent className="p-12 text-center">
              <Bug className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
              <h3 className="text-lg font-semibold mb-2">No Bug Reports Found</h3>
              <p className="text-muted-foreground">
                Create your first bug report to get started
              </p>
            </CardContent>
          </Card>
        ) : (
          filteredBugReports.map((bug) => (
            <Card key={bug.id}>
              <CardHeader>
                <div className="flex items-start justify-between">
                  <div>
                    <CardTitle className="text-lg">{bug.title}</CardTitle>
                    <CardDescription className="mt-1">
                      Created {new Date(bug.createdAt).toLocaleDateString()}
                    </CardDescription>
                  </div>
                  <div className="flex gap-2">
                    <Badge className={severityColors[bug.severity]}>
                      {bug.severity}
                    </Badge>
                    <Badge className={statusColors[bug.status]}>
                      {bug.status}
                    </Badge>
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                <p className="text-sm text-muted-foreground mb-4">{bug.description}</p>
                
                {bug.stepsToReproduce && bug.stepsToReproduce.length > 0 && (
                  <div className="mb-4">
                    <h4 className="font-medium text-sm mb-2">Steps to Reproduce:</h4>
                    <ol className="text-sm text-muted-foreground space-y-1">
                      {bug.stepsToReproduce.map((step, index) => (
                        <li key={index}>{index + 1}. {step}</li>
                      ))}
                    </ol>
                  </div>
                )}

                {bug.code && (
                  <div className="mb-4">
                    <h4 className="font-medium text-sm mb-2 flex items-center gap-2">
                      <Code className="h-4 w-4" />
                      Related Code ({bug.language})
                    </h4>
                    <pre className="text-xs bg-muted p-3 rounded overflow-x-auto">
                      {bug.code}
                    </pre>
                  </div>
                )}

                {renderAIAnalysis(bug.aiAnalysis)}
              </CardContent>
            </Card>
          ))
        )}
      </div>
    </div>
  );
};

export default BugReports;
