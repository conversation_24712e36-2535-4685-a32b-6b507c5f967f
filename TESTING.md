# Testing Guide

This guide provides comprehensive testing examples for the Test Automation System.

## Prerequisites

1. Ensure the server is running: `npm start`
2. Server should be accessible at: http://localhost:3001
3. For API testing, you need a valid DeepSeek API key in your `.env` file

## Web UI Testing

1. **Open the web interface**: http://localhost:3001
2. **Test basic functionality**:
   - Enter sample JavaScript code in the code textarea
   - Enter a purpose description
   - Click "Analyze Code" or "Generate Tests"
   - Observe the JSON response in the results area

### Sample Test Cases for Web UI

**Test Case 1: Simple Function**
```javascript
// Code:
function add(a, b) {
    return a + b;
}

// Purpose:
Add two numbers together
```

**Test Case 2: Function with Issues**
```javascript
// Code:
function divide(a, b) {
    return a / b;
}

// Purpose:
Divide two numbers safely
```

**Test Case 3: Complex Function**
```javascript
// Code:
function processUserData(userData) {
    if (!userData) return null;
    return {
        name: userData.name.toUpperCase(),
        email: userData.email.toLowerCase(),
        age: parseInt(userData.age)
    };
}

// Purpose:
Process and normalize user data input
```

## API Testing with PowerShell (Windows)

### Health Check
```powershell
Invoke-RestMethod -Uri "http://localhost:3001/health"
```

**Expected Response:**
```json
{
  "status": "healthy",
  "timestamp": "2025-01-08T12:30:45.123Z",
  "uptime": 123.456
}
```

### Code Analysis Tests

**Test 1: Simple Function Analysis**
```powershell
$body = @{
    code = "function add(a, b) { return a + b; }"
    purpose = "Add two numbers"
} | ConvertTo-Json

Invoke-RestMethod -Uri "http://localhost:3001/api/analyze" -Method POST -ContentType "application/json" -Body $body
```

**Test 2: Function with Security Issues**
```powershell
$body = @{
    code = "function processInput(input) { eval(input); }"
    purpose = "Process user input"
} | ConvertTo-Json

Invoke-RestMethod -Uri "http://localhost:3001/api/analyze" -Method POST -ContentType "application/json" -Body $body
```

**Test 3: Function with Performance Issues**
```powershell
$body = @{
    code = @"
function findItem(arr, target) {
    for (let i = 0; i < arr.length; i++) {
        for (let j = 0; j < arr.length; j++) {
            if (arr[i] === target) return i;
        }
    }
    return -1;
}
"@
    purpose = "Find item in array"
} | ConvertTo-Json

Invoke-RestMethod -Uri "http://localhost:3001/api/analyze" -Method POST -ContentType "application/json" -Body $body
```

### Test Generation Tests

**Test 1: Simple Function Test Generation**
```powershell
$body = @{
    code = "function multiply(a, b) { return a * b; }"
    purpose = "Multiply two numbers"
} | ConvertTo-Json

Invoke-RestMethod -Uri "http://localhost:3001/api/generate-tests" -Method POST -ContentType "application/json" -Body $body
```

**Test 2: Function Needing Fixes**
```powershell
$body = @{
    code = @"
function divide(a, b) {
    return a / b;
}
"@
    purpose = "Safely divide two numbers"
} | ConvertTo-Json

Invoke-RestMethod -Uri "http://localhost:3001/api/generate-tests" -Method POST -ContentType "application/json" -Body $body
```

## API Testing with curl (Linux/Mac)

### Health Check
```bash
curl http://localhost:3001/health
```

### Code Analysis
```bash
curl -X POST http://localhost:3001/api/analyze \
  -H "Content-Type: application/json" \
  -d '{
    "code": "function add(a, b) { return a + b; }",
    "purpose": "Add two numbers"
  }'
```

### Test Generation
```bash
curl -X POST http://localhost:3001/api/generate-tests \
  -H "Content-Type: application/json" \
  -d '{
    "code": "function subtract(a, b) { return a - b; }",
    "purpose": "Subtract two numbers"
  }'
```

## Error Testing

### Test Input Validation

**Test 1: Missing Code**
```powershell
$body = @{
    purpose = "Test purpose"
} | ConvertTo-Json

Invoke-RestMethod -Uri "http://localhost:3001/api/analyze" -Method POST -ContentType "application/json" -Body $body
```

**Expected Response:** 400 Bad Request

**Test 2: Code Too Large**
```powershell
$largeCode = "function test() { " + ("console.log('test'); " * 1000) + " }"
$body = @{
    code = $largeCode
    purpose = "Test large code"
} | ConvertTo-Json

Invoke-RestMethod -Uri "http://localhost:3001/api/analyze" -Method POST -ContentType "application/json" -Body $body
```

**Expected Response:** 400 Bad Request

**Test 3: Invalid JSON**
```bash
curl -X POST http://localhost:3001/api/analyze \
  -H "Content-Type: application/json" \
  -d '{"code": "invalid json"'
```

**Expected Response:** 400 Bad Request

### Test Rate Limiting

Run the following command multiple times quickly (>100 times in 15 minutes):
```powershell
$body = @{
    code = "function test() { return 'test'; }"
    purpose = "Test rate limiting"
} | ConvertTo-Json

Invoke-RestMethod -Uri "http://localhost:3001/api/analyze" -Method POST -ContentType "application/json" -Body $body
```

**Expected Response after 100 requests:** 429 Too Many Requests

## Postman Testing

### Collection Setup

1. Create a new collection named "Test Automation System"
2. Add the following requests:

**Request 1: Health Check**
- Method: GET
- URL: `http://localhost:3001/health`

**Request 2: Analyze Code**
- Method: POST
- URL: `http://localhost:3001/api/analyze`
- Headers: `Content-Type: application/json`
- Body (raw JSON):
```json
{
  "code": "function add(a, b) { return a + b; }",
  "purpose": "Add two numbers"
}
```

**Request 3: Generate Tests**
- Method: POST
- URL: `http://localhost:3001/api/generate-tests`
- Headers: `Content-Type: application/json`
- Body (raw JSON):
```json
{
  "code": "function divide(a, b) { return a / b; }",
  "purpose": "Divide two numbers safely"
}
```

### Environment Variables in Postman

Create an environment with:
- `baseUrl`: `http://localhost:3001`

Then use `{{baseUrl}}` in your requests.

## Expected Response Formats

### Successful Analysis Response
```json
{
  "status": "success",
  "data": {
    "security": ["No security issues found"],
    "performance": ["No performance issues found"],
    "optimization": ["Code is well optimized"],
    "functionality": ["Function correctly implements the stated purpose"]
  },
  "timestamp": "2025-01-08T12:30:45.123Z"
}
```

### Successful Test Generation Response
```json
{
  "status": "success",
  "data": {
    "tests": "describe('add', () => { test('should add two numbers', () => { expect(add(2, 3)).toBe(5); }); });",
    "fixes": []
  },
  "timestamp": "2025-01-08T12:30:45.123Z"
}
```

### Error Response
```json
{
  "status": "error",
  "message": "Code is required and must be a string"
}
```

## Performance Testing

For load testing, you can use tools like:
- Apache Bench (ab)
- Artillery
- k6

Example with Apache Bench:
```bash
ab -n 100 -c 10 -H "Content-Type: application/json" -p test-data.json http://localhost:3001/api/analyze
```

Where `test-data.json` contains:
```json
{"code":"function test(){return true;}","purpose":"Test function"}
```
