{"level":"error","message":"Analysis error: Code is required and must be a string","service":"test-automation-system","stack":"Error: Code is required and must be a string\n    at Validator.validateCode (C:\\Users\\<USER>\\Desktop\\MVP\\utils\\validator.js:6:13)\n    at analyzeCode (C:\\Users\\<USER>\\Desktop\\MVP\\controllers\\analysisController.js:11:39)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\MVP\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\Desktop\\MVP\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (C:\\Users\\<USER>\\Desktop\\MVP\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\MVP\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at C:\\Users\\<USER>\\Desktop\\MVP\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (C:\\Users\\<USER>\\Desktop\\MVP\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Users\\<USER>\\Desktop\\MVP\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\Users\\<USER>\\Desktop\\MVP\\node_modules\\express\\lib\\router\\index.js:175:3)","timestamp":"2025-06-08T12:55:54.784Z"}
{"error":"Code is required and must be a string","ip":"::1","level":"error","message":"Error occurred:","method":"POST","service":"test-automation-system","stack":"Error: Code is required and must be a string\n    at Validator.validateCode (C:\\Users\\<USER>\\Desktop\\MVP\\utils\\validator.js:6:13)\n    at analyzeCode (C:\\Users\\<USER>\\Desktop\\MVP\\controllers\\analysisController.js:11:39)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\MVP\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\Desktop\\MVP\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (C:\\Users\\<USER>\\Desktop\\MVP\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Desktop\\MVP\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at C:\\Users\\<USER>\\Desktop\\MVP\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (C:\\Users\\<USER>\\Desktop\\MVP\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Users\\<USER>\\Desktop\\MVP\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\Users\\<USER>\\Desktop\\MVP\\node_modules\\express\\lib\\router\\index.js:175:3)","timestamp":"2025-06-08T12:55:54.786Z","url":"/api/analyze"}
