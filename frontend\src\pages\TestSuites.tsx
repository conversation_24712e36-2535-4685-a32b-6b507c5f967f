import { useState, useEffect } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Textarea } from '@/components/ui/textarea';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  Code, 
  TestTube, 
  Shield, 
  Zap, 
  User, 
  Bug, 
  Play, 
  Download,
  FileText,
  CheckCircle,
  AlertTriangle,
  Clock,
  Target
} from 'lucide-react';
import { toast } from 'sonner';

const TestSuites = () => {
  const { user } = useAuth();
  const [code, setCode] = useState('');
  const [purpose, setPurpose] = useState('');
  const [language, setLanguage] = useState('javascript');
  const [githubUrl, setGithubUrl] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [testResults, setTestResults] = useState(null);
  const [activeRole, setActiveRole] = useState(user?.role || 'developer');

  const roles = [
    {
      id: 'developer',
      name: 'Developer',
      icon: Code,
      description: 'Unit tests, Integration tests, Code quality',
      color: 'bg-blue-500'
    },
    {
      id: 'tester',
      name: 'Tester',
      icon: TestTube,
      description: 'Functional tests, System tests, Security tests',
      color: 'bg-green-500'
    },
    {
      id: 'product_manager',
      name: 'Product Manager',
      icon: User,
      description: 'Business logic, User stories, Bug identification',
      color: 'bg-purple-500'
    }
  ];

  const languages = [
    'javascript', 'python', 'java', 'typescript', 'csharp', 'cpp', 'php', 'ruby', 'go', 'rust',
    'swift', 'kotlin', 'scala', 'dart', 'r', 'matlab', 'perl', 'lua', 'haskell', 'clojure',
    'elixir', 'erlang', 'fsharp', 'vb', 'cobol', 'fortran', 'assembly', 'sql', 'html', 'css'
  ];

  const generateRoleBasedTests = async () => {
    if (!code.trim() && !githubUrl.trim()) {
      toast.error('Please provide code or GitHub URL');
      return;
    }

    setIsLoading(true);
    try {
      const response = await fetch('/api/generate-role-tests', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        },
        body: JSON.stringify({
          code,
          purpose,
          language,
          role: activeRole,
          githubUrl
        })
      });

      const data = await response.json();
      
      if (data.status === 'success') {
        setTestResults(data.data);
        toast.success(`${roles.find(r => r.id === activeRole)?.name} test suite generated successfully!`);
      } else {
        toast.error(data.error || 'Failed to generate tests');
      }
    } catch (error) {
      toast.error('Network error occurred');
      console.error('Test generation error:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const downloadTests = () => {
    if (!testResults) return;
    
    const content = JSON.stringify(testResults, null, 2);
    const blob = new Blob([content], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `${activeRole}_test_suite_${Date.now()}.json`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  const renderTestSection = (title, tests, icon: any) => {
    const Icon = icon;
    if (!tests || tests.length === 0) return null;

    return (
      <Card className="mb-6">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Icon className="h-5 w-5" />
            {title}
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            {Array.isArray(tests) ? tests.map((test, index) => (
              <div key={index} className="p-3 bg-muted rounded-lg">
                <p className="text-sm">{test}</p>
              </div>
            )) : (
              <div className="p-3 bg-muted rounded-lg">
                <pre className="text-sm whitespace-pre-wrap">{tests}</pre>
              </div>
            )}
          </div>
        </CardContent>
      </Card>
    );
  };

  return (
    <div className="container mx-auto px-4 py-8 max-w-6xl">
      <div className="mb-8">
        <h1 className="text-4xl font-bold mb-2">Role-Based Test Suites</h1>
        <p className="text-muted-foreground">
          Generate comprehensive test suites tailored to your role and requirements
        </p>
      </div>

      {/* Role Selection */}
      <div className="grid md:grid-cols-3 gap-4 mb-8">
        {roles.map((role) => {
          const Icon = role.icon;
          return (
            <Card 
              key={role.id}
              className={`cursor-pointer transition-all duration-300 hover:scale-105 ${
                activeRole === role.id ? 'ring-2 ring-primary' : ''
              }`}
              onClick={() => setActiveRole(role.id)}
            >
              <CardContent className="p-6 text-center">
                <div className={`w-12 h-12 ${role.color} rounded-full flex items-center justify-center mx-auto mb-3`}>
                  <Icon className="h-6 w-6 text-white" />
                </div>
                <h3 className="font-semibold mb-2">{role.name}</h3>
                <p className="text-sm text-muted-foreground">{role.description}</p>
                {activeRole === role.id && (
                  <Badge className="mt-2">Selected</Badge>
                )}
              </CardContent>
            </Card>
          );
        })}
      </div>

      <div className="grid lg:grid-cols-2 gap-8">
        {/* Input Section */}
        <div className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Code Input</CardTitle>
              <CardDescription>
                Provide your code for {roles.find(r => r.id === activeRole)?.name.toLowerCase()} testing
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <label className="text-sm font-medium mb-2 block">Programming Language</label>
                <Select value={language} onValueChange={setLanguage}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {languages.map(lang => (
                      <SelectItem key={lang} value={lang}>
                        {lang.charAt(0).toUpperCase() + lang.slice(1)}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div>
                <label className="text-sm font-medium mb-2 block">Purpose/Description</label>
                <Input
                  placeholder="Describe what your code does..."
                  value={purpose}
                  onChange={(e) => setPurpose(e.target.value)}
                />
              </div>

              <div>
                <label className="text-sm font-medium mb-2 block">GitHub URL (Optional)</label>
                <Input
                  placeholder="https://github.com/username/repository"
                  value={githubUrl}
                  onChange={(e) => setGithubUrl(e.target.value)}
                />
              </div>

              <div>
                <label className="text-sm font-medium mb-2 block">Code</label>
                <Textarea
                  placeholder="Paste your code here..."
                  value={code}
                  onChange={(e) => setCode(e.target.value)}
                  rows={12}
                  className="font-mono text-sm"
                />
              </div>

              <Button 
                onClick={generateRoleBasedTests}
                disabled={isLoading}
                className="w-full"
                size="lg"
              >
                {isLoading ? (
                  <>
                    <Clock className="mr-2 h-4 w-4 animate-spin" />
                    Generating Tests...
                  </>
                ) : (
                  <>
                    <Play className="mr-2 h-4 w-4" />
                    Generate {roles.find(r => r.id === activeRole)?.name} Tests
                  </>
                )}
              </Button>
            </CardContent>
          </Card>
        </div>

        {/* Results Section */}
        <div className="space-y-6">
          {testResults ? (
            <Card>
              <CardHeader>
                <div className="flex items-center justify-between">
                  <CardTitle className="flex items-center gap-2">
                    <CheckCircle className="h-5 w-5 text-green-500" />
                    Test Results
                  </CardTitle>
                  <Button onClick={downloadTests} variant="outline" size="sm">
                    <Download className="mr-2 h-4 w-4" />
                    Download
                  </Button>
                </div>
                <CardDescription>
                  {roles.find(r => r.id === activeRole)?.name} test suite for {testResults.language}
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="max-h-96 overflow-y-auto">
                  {/* Developer Tests */}
                  {activeRole === 'developer' && (
                    <>
                      {renderTestSection('Unit Tests', testResults.unitTests, TestTube)}
                      {renderTestSection('Integration Tests', testResults.integrationTests, Zap)}
                      {renderTestSection('Code Quality Tests', testResults.codeQualityTests, Shield)}
                      {renderTestSection('Developer Tools', testResults.developerTools, Code)}
                    </>
                  )}

                  {/* Tester Tests */}
                  {activeRole === 'tester' && (
                    <>
                      {renderTestSection('Functional Tests', testResults.functionalTests, TestTube)}
                      {renderTestSection('System Tests', testResults.systemTests, Zap)}
                      {renderTestSection('Security Tests', testResults.securityTests, Shield)}
                      {renderTestSection('User Acceptance Tests', testResults.userAcceptanceTests, User)}
                      {renderTestSection('Regression Tests', testResults.regressionTests, Target)}
                    </>
                  )}

                  {/* Product Manager Tests */}
                  {activeRole === 'product_manager' && (
                    <>
                      {renderTestSection('Business Logic Tests', testResults.businessLogicTests, Target)}
                      {renderTestSection('User Story Validation', testResults.userStoryValidation, User)}
                      {renderTestSection('Bug Identification', testResults.bugIdentification, Bug)}
                      {renderTestSection('Stakeholder Tests', testResults.stakeholderTests, Shield)}
                    </>
                  )}
                </div>
              </CardContent>
            </Card>
          ) : (
            <Card>
              <CardContent className="p-12 text-center">
                <FileText className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                <h3 className="text-lg font-semibold mb-2">No Tests Generated Yet</h3>
                <p className="text-muted-foreground">
                  Generate role-specific test suites to see results here
                </p>
              </CardContent>
            </Card>
          )}
        </div>
      </div>
    </div>
  );
};

export default TestSuites;
