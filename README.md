# Test Automation System

A stateless RESTful backend using Express.js and Node.js for code analysis and test generation via the DeepSeek API, with a minimal framework-free HTML UI.

## Features

- **Code Analysis**: Security, performance, optimization, and functionality analysis
- **Test Generation**: Automated Jest test suite generation with fixes
- **Input Validation**: Sanitization and validation of code and purpose inputs
- **Rate Limiting**: 100 requests per 15 minutes per IP
- **Logging**: Comprehensive Winston logging
- **Security**: Helmet, CORS, input sanitization
- **Minimal UI**: Framework-free HTML interface with formatted results display
- **Enhanced Display**: Results are formatted with sections, icons, and syntax highlighting
- **User-Friendly Output**: Analysis results shown in organized sections instead of raw JSON

## Prerequisites

- Node.js 18+
- DeepSeek API key

## Setup Instructions

1. **Clone and navigate to the project directory**
   ```bash
   cd test-automation-system
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Configure environment variables**
   ```bash
   cp .env.example .env
   ```

   Edit `.env` and add your DeepSeek API key:
   ```
   DEEPSEEK_API_KEY=your_actual_api_key_here
   ```

4. **Start the server**
   ```bash
   npm start
   ```

   For development with auto-restart:
   ```bash
   npm run dev
   ```

5. **Access the application**
   - Web UI: http://localhost:3001
   - Health check: http://localhost:3001/health

## API Endpoints

### POST /api/analyze
Analyzes code for security, performance, optimization, and functionality.

**Request:**
```json
{
  "code": "function add(a, b) { return a + b; }",
  "purpose": "Add two numbers"
}
```

**Response:**
```json
{
  "status": "success",
  "data": {
    "security": ["No security issues found"],
    "performance": ["No performance issues found"],
    "optimization": ["Code is well optimized"],
    "functionality": ["Function correctly implements addition"]
  },
  "timestamp": "2025-01-08T12:30:45.123Z"
}
```

### POST /api/generate-tests
Generates test cases and provides fixes for issues.

**Request:**
```json
{
  "code": "function divide(a, b) { return a / b; }",
  "purpose": "Divide two numbers"
}
```

**Response:**
```json
{
  "status": "success",
  "data": {
    "tests": "describe('divide', () => { test('should divide two numbers', () => { expect(divide(10, 2)).toBe(5); }); });",
    "fixes": [
      {
        "issue": "Division by zero not handled",
        "fixedCode": "function divide(a, b) { if (b === 0) throw new Error('Division by zero'); return a / b; }"
      }
    ]
  },
  "timestamp": "2025-01-08T12:30:45.123Z"
}
```

## Testing with curl

### Analyze Code

**Linux/Mac (bash):**
```bash
curl -X POST http://localhost:3001/api/analyze \
  -H "Content-Type: application/json" \
  -d '{
    "code": "function add(a, b) { return a + b; }",
    "purpose": "Add two numbers"
  }'
```

**Windows (PowerShell):**
```powershell
Invoke-RestMethod -Uri "http://localhost:3001/api/analyze" -Method POST -ContentType "application/json" -Body '{"code":"function add(a, b) { return a + b; }","purpose":"Add two numbers"}'
```

### Generate Tests

**Linux/Mac (bash):**
```bash
curl -X POST http://localhost:3001/api/generate-tests \
  -H "Content-Type: application/json" \
  -d '{
    "code": "function multiply(a, b) { return a * b; }",
    "purpose": "Multiply two numbers"
  }'
```

**Windows (PowerShell):**
```powershell
Invoke-RestMethod -Uri "http://localhost:3001/api/generate-tests" -Method POST -ContentType "application/json" -Body '{"code":"function multiply(a, b) { return a * b; }","purpose":"Multiply two numbers"}'
```

### Health Check

**Linux/Mac (bash):**
```bash
curl http://localhost:3001/health
```

**Windows (PowerShell):**
```powershell
Invoke-RestMethod -Uri "http://localhost:3001/health"
```

## Testing with Postman

1. **Import Collection**: Create a new collection in Postman
2. **Add requests**:
   - POST `http://localhost:3001/api/analyze`
   - POST `http://localhost:3001/api/generate-tests`
   - GET `http://localhost:3001/health`
3. **Set headers**: `Content-Type: application/json`
4. **Add request body** (for POST requests):
   ```json
   {
     "code": "your_javascript_code_here",
     "purpose": "description_of_what_code_should_do"
   }
   ```

## Input Validation

- **Code**: Max 10KB, must be valid JavaScript syntax
- **Purpose**: Max 1KB, plain text description
- **Security**: Automatic sanitization and malicious pattern detection

## Rate Limiting

- 100 requests per 15 minutes per IP address
- Returns 429 status code when exceeded
- Includes retry-after header

## Error Handling

- **400**: Bad request (validation errors)
- **429**: Too many requests (rate limit exceeded)
- **500**: Internal server error
- **502**: External API error

## Logging

Logs are stored in the `logs/` directory:
- `error.log`: Error-level logs only
- `combined.log`: All log levels

## Project Structure

```
├── controllers/          # Request handlers
├── middleware/           # Express middleware
├── routes/              # API route definitions
├── services/            # Business logic and external API calls
├── utils/               # Utility functions
├── public/              # Static frontend files
├── logs/                # Log files
├── server.js            # Main server file
├── package.json         # Dependencies and scripts
└── .env.example         # Environment variables template
```

## Security Features

- Helmet for security headers
- CORS configuration
- Input sanitization with sanitize-html
- Rate limiting
- Malicious pattern detection
- Environment variable protection

## Development

For development with auto-restart:
```bash
npm run dev
```

This uses nodemon to automatically restart the server when files change.
