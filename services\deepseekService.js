const OpenAI = require('openai');
const logger = require('../utils/logger');

class DeepSeekService {
  constructor() {
    this.client = new OpenAI({
      apiKey: process.env.DEEPSEEK_API_KEY,
      baseURL: process.env.DEEPSEEK_BASE_URL || 'https://api.deepseek.com'
    });
  }

  async analyzeCode(code, purpose) {
    try {
      const prompt = this.buildAnalysisPrompt(code, purpose);

      const response = await this.client.chat.completions.create({
        model: 'deepseek-chat',
        messages: [
          {
            role: 'system',
            content: 'You are a code analysis expert. Analyze the provided code and return a structured JSON response with security, performance, optimization, and functionality analysis.'
          },
          {
            role: 'user',
            content: prompt
          }
        ],
        temperature: 0.1,
        max_tokens: 2000,
        stream: false
      });

      const analysis = this.parseAnalysisResponse(response.choices[0].message.content);
      logger.info('Code analysis completed successfully');
      return analysis;

    } catch (error) {
      logger.error('DeepSeek API error during analysis:', error);
      throw new Error(`API analysis failed: ${error.message}`);
    }
  }

  async generateTests(code, purpose) {
    try {
      const prompt = this.buildTestGenerationPrompt(code, purpose);

      const response = await this.client.chat.completions.create({
        model: 'deepseek-chat',
        messages: [
          {
            role: 'system',
            content: 'You are a test generation expert. Generate comprehensive test cases and provide fixes for any issues found in the code. Return a structured JSON response.'
          },
          {
            role: 'user',
            content: prompt
          }
        ],
        temperature: 0.2,
        max_tokens: 3000,
        stream: false
      });

      const testData = this.parseTestResponse(response.choices[0].message.content);
      logger.info('Test generation completed successfully');
      return testData;

    } catch (error) {
      logger.error('DeepSeek API error during test generation:', error);
      throw new Error(`API test generation failed: ${error.message}`);
    }
  }

  buildAnalysisPrompt(code, purpose) {
    return `
Analyze the following JavaScript code and identify potential errors, their types, and locations:

Code:
\`\`\`javascript
${code}
\`\`\`

Purpose: ${purpose}

Perform a comprehensive analysis focusing on:

1. **SECURITY VULNERABILITIES**: Look for XSS, injection attacks, unsafe patterns like eval(), innerHTML usage, etc.
2. **PERFORMANCE ISSUES**: Identify inefficient loops, memory leaks, blocking operations, unnecessary computations
3. **LOGIC ERRORS**: Find potential runtime errors, null/undefined references, type mismatches, missing validations
4. **OPTIMIZATION OPPORTUNITIES**: Suggest code improvements, best practices, refactoring opportunities

For each issue found, specify:
- The exact location (line number if possible)
- The type of error/issue
- Why it's problematic
- How to fix it

Return your analysis as a JSON object with this exact structure:
{
  "security": [
    "Detailed security findings with line numbers and fix suggestions, or 'No security issues found'"
  ],
  "performance": [
    "Performance issues with specific locations and optimization suggestions, or 'No performance issues found'"
  ],
  "optimization": [
    "Code improvement suggestions with specific examples and locations, or 'Code is well optimized'"
  ],
  "functionality": [
    "Assessment of how well code meets the purpose, including potential logic errors"
  ]
}

Be very specific about:
- Line numbers where issues occur
- Exact error types (e.g., "Potential null reference error at line 5")
- Specific fix recommendations
- Code examples of better implementations

Example format for findings:
"Line 3: Potential XSS vulnerability - using innerHTML without sanitization. Fix: Use textContent or sanitize input"
"Line 7: Performance issue - inefficient nested loop with O(n²) complexity. Fix: Use Map for O(n) lookup"
"Line 12: Logic error - missing null check before property access. Fix: Add obj?.property or if(obj) check"`;
  }

  buildTestGenerationPrompt(code, purpose) {
    return `
Generate comprehensive test cases for the following JavaScript code using Jest framework.
Also provide fixes for any critical issues found.

Code:
\`\`\`javascript
${code}
\`\`\`

Purpose: ${purpose}

Return your response as a JSON object with this exact structure:
{
  "tests": "complete Jest test suite as a string",
  "fixes": [
    {
      "issue": "description of the issue",
      "fixedCode": "corrected version of the problematic code"
    }
  ]
}

Include edge cases, error handling, and boundary testing in your test suite.`;
  }

  parseAnalysisResponse(content) {
    try {
      // Extract JSON from the response
      const jsonMatch = content.match(/\{[\s\S]*\}/);
      if (jsonMatch) {
        return JSON.parse(jsonMatch[0]);
      }

      // Fallback parsing if JSON is not properly formatted
      return {
        security: ['Analysis completed but response format was unexpected'],
        performance: ['Analysis completed but response format was unexpected'],
        optimization: ['Analysis completed but response format was unexpected'],
        functionality: ['Analysis completed but response format was unexpected']
      };
    } catch (error) {
      logger.error('Failed to parse analysis response:', error);
      return {
        security: ['Failed to parse analysis results'],
        performance: ['Failed to parse analysis results'],
        optimization: ['Failed to parse analysis results'],
        functionality: ['Failed to parse analysis results']
      };
    }
  }

  parseTestResponse(content) {
    try {
      // Extract JSON from the response
      const jsonMatch = content.match(/\{[\s\S]*\}/);
      if (jsonMatch) {
        return JSON.parse(jsonMatch[0]);
      }

      // Fallback
      return {
        tests: '// Test generation completed but response format was unexpected',
        fixes: []
      };
    } catch (error) {
      logger.error('Failed to parse test response:', error);
      return {
        tests: '// Failed to parse test generation results',
        fixes: []
      };
    }
  }
}

module.exports = new DeepSeekService();
