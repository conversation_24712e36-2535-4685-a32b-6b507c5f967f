@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    /* Light Mode - Vibrant Purple/Pink Theme */
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;
    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;
    --primary: 262 83% 58%; /* Vibrant Purple */
    --primary-foreground: 210 40% 98%;
    --secondary: 340 82% 52%; /* Vibrant Pink */
    --secondary-foreground: 222.2 84% 4.9%;
    --muted: 210 40% 96%;
    --muted-foreground: 215.4 16.3% 46.9%;
    --accent: 195 100% 50%; /* Vibrant <PERSON>an */
    --accent-foreground: 222.2 84% 4.9%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;
    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 262 83% 58%;
    --chart-1: 262 83% 58%; /* Purple */
    --chart-2: 340 82% 52%; /* Pink */
    --chart-3: 195 100% 50%; /* <PERSON>an */
    --chart-4: 120 100% 40%; /* Green */
    --chart-5: 45 100% 50%; /* Orange */
    --radius: 0.5rem;
  }

  .dark {
    /* Dark Mode - Vibrant Emerald/Orange Theme */
    --background: 240 10% 3.9%; /* Deep Dark Blue */
    --foreground: 0 0% 98%;
    --card: 240 10% 3.9%;
    --card-foreground: 0 0% 98%;
    --popover: 240 10% 3.9%;
    --popover-foreground: 0 0% 98%;
    --primary: 142 76% 36%; /* Vibrant Emerald */
    --primary-foreground: 355.7 100% 97.3%;
    --secondary: 240 3.7% 15.9%; /* Dark Gray */
    --secondary-foreground: 0 0% 98%;
    --muted: 240 3.7% 15.9%;
    --muted-foreground: 240 5% 64.9%;
    --accent: 12 76% 61%; /* Vibrant Orange */
    --accent-foreground: 0 0% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 0% 98%;
    --border: 240 3.7% 15.9%;
    --input: 240 3.7% 15.9%;
    --ring: 142 76% 36%;
    --chart-1: 142 76% 36%; /* Emerald */
    --chart-2: 12 76% 61%; /* Orange */
    --chart-3: 262 83% 58%; /* Purple */
    --chart-4: 195 100% 50%; /* Cyan */
    --chart-5: 340 82% 52%; /* Pink */
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  @apply bg-muted;
}

::-webkit-scrollbar-thumb {
  @apply bg-muted-foreground/30 rounded-md;
}

::-webkit-scrollbar-thumb:hover {
  @apply bg-muted-foreground/50;
}

/* Code syntax highlighting */
.syntax-keyword {
  @apply text-blue-400 font-medium;
}

.syntax-string {
  @apply text-green-400;
}

.syntax-comment {
  @apply text-gray-500 italic;
}

.syntax-number {
  @apply text-orange-400;
}

.syntax-boolean {
  @apply text-purple-400;
}

/* Animations */
@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-10px); }
}

.animate-float {
  animation: float 3s ease-in-out infinite;
}

@keyframes glow {
  0%, 100% { box-shadow: 0 0 5px rgba(59, 130, 246, 0.5); }
  50% { box-shadow: 0 0 20px rgba(59, 130, 246, 0.8); }
}

.animate-glow {
  animation: glow 2s ease-in-out infinite;
}
